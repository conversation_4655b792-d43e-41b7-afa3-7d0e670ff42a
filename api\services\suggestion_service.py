"""Interest suggestion related Facebook API operations."""

import logging
from typing import List

import httpx

from api.models.admin import AdminStore
from api.schemas import InterestItem
from api.utils.async_utils import process_items_concurrently
from .facebook_base import FacebookBaseService

logger = logging.getLogger(__name__)


class SuggestionService(FacebookBaseService):
    async def fetch_suggestions(
        self,
        seed_interests: List[InterestItem],
        limit: int = 1000,
    ) -> List[InterestItem]:
        """Fetch interest suggestions for a list of seed interests."""
        logger.info("Fetching suggestions for %d seed interests", len(seed_interests))

        async def fetch_single_suggestion(seed_interest: InterestItem) -> List[InterestItem]:
            ad_account_id = (
                self.ad_account_id.replace("act_", "")
                if str(self.ad_account_id).startswith("act_")
                else self.ad_account_id
            )
            endpoint = f"https://graph.facebook.com/v22.0/act_{ad_account_id}/targetingsuggestions"

            admin_store = AdminStore()
            credentials = admin_store.get_credentials()

            params = {
                "locale": "en_US",
                "type": "adinterest",
                "q": seed_interest.name,
                "fields": "id,name,audience_size_lower_bound,audience_size_upper_bound,path,topic,description,type,popularity,is_trending,is_unavailable",
                "limit": limit,
                "access_token": credentials.access_token,
            }

            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(endpoint, params=params)
                    if response.status_code == 200:
                        data = response.json()
                        suggestions = []
                        for item in data.get("data", []):
                            suggestions.append(
                                InterestItem(
                                    id=item.get("id", ""),
                                    name=item.get("name", ""),
                                    audience_size_lower_bound=item.get("audience_size_lower_bound", 0),
                                    audience_size_upper_bound=item.get("audience_size_upper_bound", 0),
                                    path=item.get("path", []),
                                    topic=item.get("topic", ""),
                                    description=item.get("description", ""),
                                    disambiguation_category=item.get("disambiguation_category", ""),
                                    type=item.get("type", "interests"),
                                )
                            )
                        logger.info(
                            "Found %d suggestions for: %s", len(suggestions), seed_interest.name
                        )
                        return suggestions
                    logger.error(
                        "Error fetching suggestions for %s: %s - %s",
                        seed_interest.name,
                        response.status_code,
                        response.text,
                    )
                    return []
            except Exception as e:
                logger.error(
                    "Exception fetching suggestions for %s: %s", seed_interest.name, str(e)
                )
                return []

        results = await process_items_concurrently(
            items=seed_interests,
            process_func=fetch_single_suggestion,
            concurrency=5,
            flatten_results=True,
        )
        return results
