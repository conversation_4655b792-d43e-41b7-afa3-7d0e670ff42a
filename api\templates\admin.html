<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - TargetWise</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --color-primary: #2563eb;
            --color-primary-hover: #1d4ed8;
            --color-success: #10b981;
            --color-danger: #ef4444;
            --color-warning: #f59e0b;
            --color-text: #1f2937;
            --color-text-secondary: #6b7280;
            --color-border: #e5e7eb;
            --color-bg-light: #f9fafb;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--color-bg-light);
            color: var(--color-text);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .tw-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .tw-card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--color-border);
            background-color: #fafafa;
        }
        
        .tw-card-body {
            padding: 1.5rem;
        }
        
        .tw-btn {
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s;
            display: inline-block;
            border: 1px solid transparent;
            cursor: pointer;
        }
        
        .tw-btn-primary {
            background-color: var(--color-primary);
            color: white;
        }
        
        .tw-btn-primary:hover {
            background-color: var(--color-primary-hover);
            color: white;
        }
        
        .tw-btn-outline-primary {
            border-color: var(--color-primary);
            color: var(--color-primary);
            background: white;
        }
        
        .tw-btn-outline-primary:hover {
            background-color: var(--color-primary);
            color: white;
        }
        
        .tw-btn-outline-secondary {
            border-color: var(--color-border);
            color: var(--color-text-secondary);
            background: white;
        }
        
        .tw-btn-success {
            background-color: var(--color-success);
            color: white;
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }
        
        .status-indicator.connected {
            background-color: var(--color-success);
        }
        
        .status-indicator.disconnected {
            background-color: var(--color-danger);
        }
        
        .status-indicator.unknown {
            background-color: var(--color-warning);
        }
        
        .tw-form-control, .tw-form-select {
            padding: 0.5rem 0.75rem;
            border: 1px solid var(--color-border);
            border-radius: 0.375rem;
            width: 100%;
            font-size: 1rem;
        }
        
        .tw-form-control:focus, .tw-form-select:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .tw-form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .form-text {
            font-size: 0.875rem;
            color: var(--color-text-secondary);
            margin-top: 0.25rem;
        }
        
        .gap-2 {
            gap: 0.5rem;
        }
        
        .footer {
            text-align: center;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid var(--color-border);
            color: var(--color-text-secondary);
        }
    </style>
</head>
<body>
<div class="container admin-page">
    <div class="header d-flex justify-content-between align-items-center">
        <div>
            <h1>Admin Dashboard</h1>
            <p class="lead">Manage Facebook API credentials and settings</p>
        </div>
        <div class="d-flex gap-2">
            <a href="/" class="tw-btn tw-btn-outline-primary">Back to API</a>
            <a href="/logs" class="tw-btn tw-btn-outline-secondary">View Logs</a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="tw-card mb-4">
                <div class="tw-card-header d-flex justify-content-between align-items-center">
                    <h5>Facebook API Credentials</h5>
                    <div>
                        <span class="status-indicator {{ 'connected' if api_status == 'connected' else 'disconnected' if api_status == 'disconnected' else 'unknown' }}"></span>
                        <span>{{ 'Connected' if api_status == 'connected' else 'Disconnected' if api_status == 'disconnected' else 'Unknown' }}</span>
                    </div>
                </div>
                <div class="tw-card-body">
                    {% if message %}
                    <div class="alert alert-{{ message_type }}" role="alert">
                        {{ message }}
                    </div>
                    {% endif %}

                    <form id="api-credentials-form" method="post" action="/admin/save-credentials">
                        <div class="mb-3">
                            <label for="api_version" class="tw-form-label">API Version</label>
                            <input type="text" class="tw-form-control" id="api_version" name="api_version" value="{{ credentials.api_version }}" required>
                            <div class="form-text">Example: v17.0</div>
                        </div>
                        <div class="mb-3">
                            <label for="ad_account_id" class="tw-form-label">Ad Account ID</label>
                            <input type="text" class="tw-form-control" id="ad_account_id" name="ad_account_id" value="{{ credentials.ad_account_id }}" required>
                            <div class="form-text">Example: act_123456789</div>
                        </div>
                        <div class="mb-3">
                            <label for="access_token" class="tw-form-label">Access Token</label>
                            <input type="password" class="tw-form-control" id="access_token" name="access_token" value="{{ credentials.access_token }}" required>
                            <div class="form-text">Your Facebook API access token with ads_management permission</div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="tw-btn tw-btn-primary">Save Credentials</button>
                            <button type="button" class="tw-btn tw-btn-success" id="test-connection-tw-btn">Test Connection</button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="tw-card mb-4">
                <div class="tw-card-header">
                    <h5>Application Settings</h5>
                </div>
                <div class="tw-card-body">
                    <form id="app-settings-form" method="post" action="/admin/save-settings">
                        <div class="mb-3">
                            <label for="target_audience_min" class="tw-form-label">Target Audience Size (Min)</label>
                            <input type="number" class="tw-form-control" id="target_audience_min" name="target_audience_min" value="{{ settings.target_audience_min }}" required>
                            <div class="form-text">Minimum target audience size in millions (default: 4)</div>
                        </div>
                        <div class="mb-3">
                            <label for="target_audience_max" class="tw-form-label">Target Audience Size (Max)</label>
                            <input type="number" class="tw-form-control" id="target_audience_max" name="target_audience_max" value="{{ settings.target_audience_max }}" required>
                            <div class="form-text">Maximum target audience size in millions (default: 5)</div>
                        </div>
                        <button type="submit" class="tw-btn tw-btn-primary">Save Settings</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="tw-card mb-4">
                <div class="tw-card-header">
                    <h5>API Status</h5>
                </div>
                <div class="tw-card-body">
                    <div id="api-status">
                        <div class="mb-3">
                            <strong>Last Check:</strong> <span id="last-check">{{ last_check }}</span>
                        </div>
                        <div class="mb-3">
                            <strong>Status:</strong>
                            <span class="status-indicator {{ 'connected' if api_status == 'connected' else 'disconnected' if api_status == 'disconnected' else 'unknown' }}"></span>
                            <span id="status-text">{{ 'Connected' if api_status == 'connected' else 'Disconnected' if api_status == 'disconnected' else 'Unknown' }}</span>
                        </div>
                        <div class="mb-3">
                            <strong>Rate Limit:</strong> <span id="rate-limit">{{ rate_limit }}</span>
                        </div>
                    </div>
                    <button type="button" class="tw-btn tw-btn-outline-primary w-100" id="refresh-status-tw-btn">Refresh Status</button>
                </div>
            </div>

            <div class="tw-card mb-4">
                <div class="tw-card-header">
                    <h5>Browse Targeting Categories</h5>
                </div>
                <div class="tw-card-body">
                    <div class="mb-3">
                        <label for="targeting-class" class="tw-form-label">Targeting Class</label>
                        <select class="tw-form-select" id="targeting-class">
                            <option value="demographics">Demographics</option>
                            <option value="interests">Interests</option>
                            <option value="behaviors">Behaviors</option>
                            <option value="life_events">Life Events</option>
                        </select>
                    </div>
                    <button type="button" class="tw-btn tw-btn-primary w-100 mb-3" id="browse-targeting-tw-btn">Browse Categories</button>

                    <div id="targeting-results" class="d-none">
                        <h6>Results</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Path</th>
                                        <th>Audience Size</th>
                                    </tr>
                                </thead>
                                <tbody id="targeting-results-body"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tw-card mb-4">
                <div class="tw-card-header">
                    <h5>Help</h5>
                </div>
                <div class="tw-card-body">
                    <h6>Getting Facebook API Credentials</h6>
                    <ol>
                        <li>Go to <a href="https://developers.facebook.com/" target="_blank">Facebook Developers</a></li>
                        <li>Create a new app or use an existing one</li>
                        <li>Add the Marketing API product to your app</li>
                        <li>Generate a token with ads_management permission</li>
                        <li>Find your Ad Account ID in Facebook Ads Manager</li>
                    </ol>
                    <a href="https://developers.facebook.com/docs/marketing-apis/get-started" target="_blank" class="tw-btn tw-btn-outline-secondary w-100">Facebook API Documentation</a>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>TargetWise API v2.0 | Admin Dashboard</p>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const testConnectionBtn = document.getElementById('test-connection-tw-btn');
        if (testConnectionBtn) {
            testConnectionBtn.addEventListener('click', function() {
                const apiVersion = document.getElementById('api_version').value;
                const adAccountId = document.getElementById('ad_account_id').value;
                const accessToken = document.getElementById('access_token').value;
                if (!apiVersion || !adAccountId || !accessToken) {
                    alert('Please fill in all credential fields');
                    return;
                }
                testConnectionBtn.disabled = true;
                testConnectionBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Testing...';
                fetch('/admin/test-connection', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ api_version: apiVersion, ad_account_id: adAccountId, access_token: accessToken })
                })
                .then(r => r.json())
                .then(data => {
                    if (data.success) { alert('Connection successful! API credentials are valid.'); }
                    else { alert('Connection failed: ' + data.error); }
                })
                .catch(error => { alert('Error testing connection: ' + error); })
                .finally(() => {
                    testConnectionBtn.disabled = false;
                    testConnectionBtn.innerHTML = 'Test Connection';
                });
            });
        }

        const refreshStatusBtn = document.getElementById('refresh-status-tw-btn');
        if (refreshStatusBtn) {
            refreshStatusBtn.addEventListener('click', function() {
                refreshStatusBtn.disabled = true;
                refreshStatusBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
                fetch('/admin/api-status')
                .then(r => r.json())
                .then(data => {
                    document.getElementById('last-check').textContent = data.last_check;
                    document.getElementById('status-text').textContent = data.status === 'connected' ? 'Connected' : data.status === 'disconnected' ? 'Disconnected' : 'Unknown';
                    document.getElementById('rate-limit').textContent = data.rate_limit;
                    document.querySelectorAll('.status-indicator').forEach(indicator => {
                        indicator.classList.remove('connected', 'disconnected', 'unknown');
                        indicator.classList.add(data.status);
                    });
                })
                .catch(error => { console.error('Error refreshing status:', error); })
                .finally(() => {
                    refreshStatusBtn.disabled = false;
                    refreshStatusBtn.innerHTML = 'Refresh Status';
                });
            });
        }

        const browseTargetingBtn = document.getElementById('browse-targeting-tw-btn');
        if (browseTargetingBtn) {
            browseTargetingBtn.addEventListener('click', function() {
                const targetingClass = document.getElementById('targeting-class').value;
                browseTargetingBtn.disabled = true;
                browseTargetingBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
                document.getElementById('targeting-results').classList.add('d-none');
                fetch(`/admin/browse-targeting?targeting_class=${targetingClass}`)
                .then(r => r.json())
                .then(data => {
                    if (data.success) {
                        const resultsBody = document.getElementById('targeting-results-body');
                        resultsBody.innerHTML = '';
                        if (data.data && data.data.length > 0) {
                            data.data.forEach(item => {
                                const row = document.createElement('tr');
                                const idCell = document.createElement('td'); idCell.textContent = item.id || ''; row.appendChild(idCell);
                                const nameCell = document.createElement('td'); nameCell.textContent = item.name || ''; row.appendChild(nameCell);
                                const typeCell = document.createElement('td'); typeCell.textContent = item.type || ''; row.appendChild(typeCell);
                                const pathCell = document.createElement('td'); pathCell.textContent = item.path ? item.path.join(' > ') : ''; row.appendChild(pathCell);
                                const audienceCell = document.createElement('td');
                                if (item.audience_size_lower_bound && item.audience_size_upper_bound) {
                                    audienceCell.textContent = `${formatNumber(item.audience_size_lower_bound)} - ${formatNumber(item.audience_size_upper_bound)}`;
                                } else { audienceCell.textContent = 'N/A'; }
                                row.appendChild(audienceCell);
                                resultsBody.appendChild(row);
                            });
                            document.getElementById('targeting-results').classList.remove('d-none');
                        } else { alert('No targeting categories found.'); }
                    } else {
                        alert('Error browsing targeting categories: ' + data.error);
                    }
                })
                .catch(error => { alert('Error browsing targeting categories: ' + error); })
                .finally(() => {
                    browseTargetingBtn.disabled = false;
                    browseTargetingBtn.innerHTML = 'Browse Categories';
                });
            });
        }

        function formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }
    });
</script>
</body>
</html>