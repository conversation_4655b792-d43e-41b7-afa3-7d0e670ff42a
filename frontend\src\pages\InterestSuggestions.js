import React, { useState, useEffect } from 'react';
import '../styles/pages/interest-suggestions.css';

const InterestSuggestions = () => {
    const [activeTab, setActiveTab] = useState('single');
    const [selectedSeed, setSelectedSeed] = useState('');
    const [suggestions, setSuggestions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [selectedSuggestions, setSelectedSuggestions] = useState(new Set());
    const [cartCount, setCartCount] = useState(0);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedSeedItem, setSelectedSeedItem] = useState(null);

    const seedLibrary = [
        { id: 'movies', name: 'Movies', category: 'Entertainment', icon: '🎬', audience: '850M' },
        { id: 'technology', name: 'Technology', category: 'Interests', icon: '📱', audience: '720M' },
        { id: 'fitness', name: 'Fitness', category: 'Health & Wellness', icon: '🏃', audience: '540M' },
        { id: 'cooking', name: 'Cooking', category: 'Food & Drink', icon: '🍳', audience: '430M' },
        { id: 'travel', name: 'Travel', category: 'Lifestyle', icon: '✈️', audience: '380M' }
    ];

    const mockSuggestions = [
        { id: 1, name: 'Netflix', audience: '420M', category: 'Streaming' },
        { id: 2, name: 'HBO', audience: '180M', category: 'Entertainment' },
        { id: 3, name: 'Film Festival', audience: '125M', category: 'Events' },
        { id: 4, name: 'IMAX', audience: '95M', category: 'Cinema' },
        { id: 5, name: 'Popcorn', audience: '340M', category: 'Food' },
        { id: 6, name: 'Red Carpet', audience: '78M', category: 'Entertainment' },
        { id: 7, name: 'Action Movies', audience: '560M', category: 'Genre' },
        { id: 8, name: 'Documentary', audience: '290M', category: 'Genre' }
    ];

    const handleSeedItemClick = (seed) => {
        setSelectedSeedItem(seed.id);
        setSelectedSeed(seed.id);
    };

    const handleGetSuggestions = async (e) => {
        e.preventDefault();
        
        if (!selectedSeed) {
            return;
        }

        setLoading(true);
        setSuggestions([]);
        
        // Simulate API call
        setTimeout(() => {
            setSuggestions(mockSuggestions);
            setLoading(false);
        }, 1500);
    };

    const handleSuggestionSelect = (suggestionId) => {
        const newSelected = new Set(selectedSuggestions);
        if (newSelected.has(suggestionId)) {
            newSelected.delete(suggestionId);
        } else {
            newSelected.add(suggestionId);
        }
        setSelectedSuggestions(newSelected);
    };

    const handleAddToCart = (e, suggestionId) => {
        e.stopPropagation();
        setCartCount(cartCount + 1);
    };

    const handleDeselectAll = () => {
        setSelectedSuggestions(new Set());
    };

    const handleAddSelectedToCart = () => {
        setCartCount(cartCount + selectedSuggestions.size);
        setSelectedSuggestions(new Set());
    };

    const filteredSeeds = seedLibrary.filter(seed => 
        seed.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        seed.category.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
        <>
            {/* Header */}
            <header className="header">
                <div className="header-content">
                    <a href="#" className="logo">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <circle cx="12" cy="12" r="6"></circle>
                            <circle cx="12" cy="12" r="2"></circle>
                        </svg>
                        TargetWise
                    </a>
                    
                    <nav className="nav-menu">
                        <a href="#" className="nav-link">Home</a>
                        <a href="#" className="nav-link active">Micro-Tools</a>
                        <a href="#" className="nav-link">Documentation</a>
                        <a href="#" className="nav-link">Pricing</a>
                        <a href="#" className="nav-link">Admin</a>
                    </nav>
                    
                    <div className="header-actions">
                        <button className="theme-toggle">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <circle cx="12" cy="12" r="5"></circle>
                                <line x1="12" y1="1" x2="12" y2="3"></line>
                                <line x1="12" y1="21" x2="12" y2="23"></line>
                                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                                <line x1="1" y1="12" x2="3" y2="12"></line>
                                <line x1="21" y1="12" x2="23" y2="12"></line>
                                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                            </svg>
                        </button>
                        <a href="#" className="btn btn-outline">Log In</a>
                        <a href="#" className="btn btn-primary">Sign Up Free</a>
                    </div>
                </div>
            </header>

            {/* Main Container */}
            <div className="main-container">
                {/* Sidebar */}
                <aside className="sidebar">
                    <h3 className="sidebar-title">Micro-Tools</h3>
                    <nav className="sidebar-menu">
                        <a href="#" className="sidebar-item">
                            <svg className="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <rect x="3" y="3" width="7" height="7"></rect>
                                <rect x="14" y="3" width="7" height="7"></rect>
                                <rect x="14" y="14" width="7" height="7"></rect>
                                <rect x="3" y="14" width="7" height="7"></rect>
                            </svg>
                            Dashboard
                        </a>
                        <a href="#" className="sidebar-item">
                            <svg className="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                            Interest Search
                        </a>
                        <a href="#" className="sidebar-item active">
                            <svg className="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>
                            </svg>
                            Interest Suggestions
                        </a>
                        <a href="#" className="sidebar-item">
                            <svg className="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M3 3v18h18"></path>
                                <path d="m19 9-5 5-4-4-3 3"></path>
                            </svg>
                            Taxonomy Browser
                        </a>
                        <a href="#" className="sidebar-item">
                            <svg className="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"></path>
                            </svg>
                            Interest Pool
                        </a>
                    </nav>
                </aside>

                {/* Content */}
                <main className="content">
                    {/* Page Header */}
                    <div className="page-header">
                        <div className="page-header-top">
                            <div>
                                <h1 className="page-title">
                                    <div className="page-icon">💡</div>
                                    Interest Suggestions
                                </h1>
                                <p className="page-subtitle">Discover related interests based on seed interests</p>
                            </div>
                            <div className="page-actions">
                                <button className="btn btn-secondary">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <path d="M9 18l6-6-6-6"></path>
                                    </svg>
                                    Back to Tools
                                </button>
                                <button className="btn staging-cart">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <circle cx="9" cy="21" r="1"></circle>
                                        <circle cx="20" cy="21" r="1"></circle>
                                        <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                                    </svg>
                                    Staging Cart
                                    <span className="cart-badge">{cartCount}</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Content Grid */}
                    <div className="content-grid">
                        {/* Seed Library */}
                        <div className="seed-library">
                            <div className="panel-header">
                                <div className="panel-icon">🌱</div>
                                <h2 className="panel-title">Seed Library</h2>
                            </div>

                            <div className="search-box">
                                <div className="search-input-wrapper">
                                    <svg className="search-input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <circle cx="11" cy="11" r="8"></circle>
                                        <path d="m21 21-4.35-4.35"></path>
                                    </svg>
                                    <input 
                                        type="text" 
                                        className="search-input" 
                                        placeholder="Search for a seed interest"
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>
                            </div>

                            <div className="seed-list">
                                {filteredSeeds.length > 0 ? (
                                    filteredSeeds.map(seed => (
                                        <div 
                                            key={seed.id} 
                                            className={`seed-item ${selectedSeedItem === seed.id ? 'selected' : ''}`}
                                            onClick={() => handleSeedItemClick(seed)}
                                        >
                                            <div className="seed-info">
                                                <div className="seed-icon">{seed.icon}</div>
                                                <div>
                                                    <div className="seed-name">{seed.name}</div>
                                                    <div className="seed-category">{seed.category}</div>
                                                </div>
                                            </div>
                                            <span className="seed-count">{seed.audience}</span>
                                        </div>
                                    ))
                                ) : (
                                    <div className="no-seeds">No matching seeds found</div>
                                )}
                            </div>
                        </div>

                        {/* Suggestions Panel */}
                        <div className="suggestions-panel">
                            <div className="panel-header">
                                <div className="panel-icon">🎯</div>
                                <h2 className="panel-title">Get Suggestions</h2>
                            </div>

                            <div className="tabs">
                                <button 
                                    className={`tab ${activeTab === 'single' ? 'active' : ''}`}
                                    onClick={() => setActiveTab('single')}
                                >
                                    Single Suggestions
                                </button>
                                <button 
                                    className={`tab ${activeTab === 'bulk' ? 'active' : ''}`}
                                    onClick={() => setActiveTab('bulk')}
                                >
                                    Bulk Suggestions
                                </button>
                            </div>

                            <form className="suggestions-form" onSubmit={handleGetSuggestions}>
                                <div className="form-group">
                                    <label className="form-label">Select a seed interest</label>
                                    <div className="select-wrapper">
                                        <select 
                                            className="form-select" 
                                            value={selectedSeed}
                                            onChange={(e) => setSelectedSeed(e.target.value)}
                                        >
                                            <option value="">-- Select a seed interest --</option>
                                            {seedLibrary.map(seed => (
                                                <option key={seed.id} value={seed.id}>
                                                    {seed.name} ({seed.category})
                                                </option>
                                            ))}
                                        </select>
                                        <svg className="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <polyline points="6 9 12 15 18 9"></polyline>
                                        </svg>
                                    </div>
                                </div>

                                <button type="submit" className="btn btn-primary btn-get-suggestions">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>
                                    </svg>
                                    Get Suggestions
                                </button>
                            </form>

                            <div className="suggestions-section">
                                {selectedSuggestions.size > 0 && (
                                    <div className="bulk-actions">
                                        <span className="bulk-info">{selectedSuggestions.size} items selected</span>
                                        <div className="bulk-buttons">
                                            <button className="btn btn-secondary" onClick={handleDeselectAll}>Deselect All</button>
                                            <button className="btn btn-primary" onClick={handleAddSelectedToCart}>Add to Cart</button>
                                        </div>
                                    </div>
                                )}

                                <div className="suggestions-header">
                                    <h3 className="suggestions-title">Suggestions</h3>
                                    <span className="suggestions-count">{suggestions.length} results</span>
                                </div>

                                {loading ? (
                                    <div className="loading">
                                        <div className="spinner"></div>
                                    </div>
                                ) : suggestions.length > 0 ? (
                                    <div className="suggestion-list">
                                        {suggestions.map(suggestion => (
                                            <div 
                                                key={suggestion.id} 
                                                className={`suggestion-item ${selectedSuggestions.has(suggestion.id) ? 'selected' : ''}`}
                                                onClick={() => handleSuggestionSelect(suggestion.id)}
                                            >
                                                <div className="suggestion-content">
                                                    <div className="suggestion-name">{suggestion.name}</div>
                                                    <div className="suggestion-meta">
                                                        <span className="suggestion-stat">
                                                            <svg className="suggestion-stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                                                <circle cx="9" cy="7" r="4"></circle>
                                                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                                                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                                            </svg>
                                                            {suggestion.audience}
                                                        </span>
                                                        <span className="suggestion-stat">
                                                            <svg className="suggestion-stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                                <path d="M3 3h18v18H3zM12 8v8m-4-4h8"></path>
                                                            </svg>
                                                            {suggestion.category}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="suggestion-actions">
                                                    <button className="action-btn" title="Add to favorites">
                                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                            <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                                        </svg>
                                                    </button>
                                                    <button 
                                                        className="action-btn add-btn" 
                                                        title="Add to cart"
                                                        onClick={(e) => handleAddToCart(e, suggestion.id)}
                                                    >
                                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                            <line x1="12" y1="5" x2="12" y2="19"></line>
                                                            <line x1="5" y1="12" x2="19" y2="12"></line>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="empty-state">
                                        <div className="empty-icon">💡</div>
                                        <h4 className="empty-title">No suggestions yet</h4>
                                        <p className="empty-text">Select a seed interest and click "Get Suggestions" to see related interests</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </>
    );
};

export default InterestSuggestions;