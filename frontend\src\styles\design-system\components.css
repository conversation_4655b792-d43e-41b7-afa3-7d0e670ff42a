/* TargetWise Design System - Reusable Components */
/* Component styles that can be composed and reused */

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-5);
  font-weight: var(--font-semibold);
  font-size: var(--text-base);
  line-height: 1.5;
  border-radius: var(--radius-md);
  transition: all var(--transition-base);
  cursor: pointer;
  white-space: nowrap;
  user-select: none;
  border: none;
  text-decoration: none;
}

.btn:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Variants */
.btn-primary {
  background: var(--gradient-primary);
  color: var(--color-white);
  box-shadow: var(--shadow-primary);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary-hover);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
}

.btn-secondary {
  background: var(--color-gray-100);
  color: var(--color-gray-600);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-gray-200);
}

.btn-outline {
  background: transparent;
  color: var(--color-gray-600);
  border: 2px solid var(--color-gray-200);
}

.btn-outline:hover:not(:disabled) {
  border-color: var(--color-gray-300);
  background: var(--color-gray-50);
}

.btn-ghost {
  background: transparent;
  color: var(--color-gray-600);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--color-gray-100);
}

.btn-danger {
  background: var(--gradient-danger);
  color: var(--color-white);
}

.btn-success {
  background: var(--gradient-success);
  color: var(--color-white);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-sm);
}

.btn-lg {
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-lg);
}

.btn-icon {
  width: 40px;
  height: 40px;
  padding: 0;
}

/* Cards */
.card {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
  padding: var(--space-8);
  transition: all var(--transition-slow);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  margin-bottom: var(--space-6);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--color-gray-800);
}

.card-subtitle {
  font-size: var(--text-sm);
  color: var(--color-gray-500);
  margin-top: var(--space-1);
}

/* Form Elements */
.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-gray-600);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--color-white);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  transition: all var(--transition-base);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--color-gray-400);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

.form-select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%2394a3b8' stroke-width='2' xmlns='http://www.w3.org/2000/svg'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right var(--space-3) center;
  padding-right: var(--space-12);
}

/* Input Groups */
.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: var(--space-4);
  color: var(--color-gray-400);
  pointer-events: none;
}

.input-group .form-input {
  padding-left: var(--space-12);
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  border-radius: var(--radius-3xl);
  white-space: nowrap;
}

.badge-primary {
  background: var(--color-primary-light);
  color: var(--color-primary);
}

.badge-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--color-success);
}

.badge-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--color-warning);
}

.badge-danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--color-danger);
}

/* Tables */
.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--color-white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th {
  text-align: left;
  padding: var(--space-4);
  font-weight: var(--font-semibold);
  font-size: var(--text-sm);
  color: var(--color-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: var(--color-gray-50);
  border-bottom: 2px solid var(--color-gray-100);
}

.table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--color-gray-100);
}

.table tr:hover {
  background: var(--color-gray-50);
}

/* Loading Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-gray-100);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--space-16) var(--space-4);
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--space-6);
  background: var(--color-gray-100);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-4xl);
}

.empty-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--color-gray-800);
  margin-bottom: var(--space-2);
}

.empty-text {
  color: var(--color-gray-500);
}

/* Tabs */
.tabs {
  display: flex;
  gap: var(--space-2);
  background: var(--color-gray-100);
  padding: var(--space-1);
  border-radius: var(--radius-lg);
}

.tab {
  flex: 1;
  padding: var(--space-2) var(--space-4);
  font-weight: var(--font-medium);
  color: var(--color-gray-500);
  background: transparent;
  border-radius: var(--radius-sm);
  transition: all var(--transition-base);
  cursor: pointer;
  border: none;
}

.tab.active {
  background: var(--color-white);
  color: var(--color-primary);
  box-shadow: var(--shadow-xs);
}

.tab:hover:not(.active) {
  color: var(--color-gray-700);
}

/* Tooltips */
.tooltip {
  position: relative;
}

.tooltip-content {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: var(--space-2) var(--space-3);
  background: var(--color-gray-800);
  color: var(--color-white);
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  z-index: var(--z-tooltip);
}

.tooltip:hover .tooltip-content {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-8px);
}

/* Checkbox */
.checkbox {
  width: 20px;
  height: 20px;
  cursor: pointer;
  accent-color: var(--color-primary);
}

/* Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--color-gray-300);
  transition: var(--transition-base);
  border-radius: var(--radius-full);
}

.switch-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background: var(--color-white);
  transition: var(--transition-base);
  border-radius: 50%;
}

.switch input:checked + .switch-slider {
  background: var(--color-primary);
}

.switch input:checked + .switch-slider:before {
  transform: translateX(24px);
}