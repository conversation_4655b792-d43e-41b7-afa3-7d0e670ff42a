# // DOC: docs/micro-tools/search-bulk-feature.md
import json
import logging
import os
from datetime import datetime
from typing import Optional

from pydantic import BaseModel

logger = logging.getLogger(__name__)


class APICredentials(BaseModel):
    """Model for storing API credentials"""

    api_version: str
    ad_account_id: str
    access_token: str
    last_updated: Optional[str] = None


class AppSettings(BaseModel):
    """Model for storing application settings"""

    target_audience_min: int = 4000000
    target_audience_max: int = 5000000
    last_updated: Optional[str] = None


class APIStatus(BaseModel):
    """Model for storing API status"""

    status: str = "unknown"  # "connected", "disconnected", "unknown"
    last_check: Optional[str] = None
    rate_limit: Optional[str] = None
    error: Optional[str] = None


class AdminStore:
    """Class for storing and retrieving admin data"""

    def __init__(self, data_dir: str = None):
        """Initialize the admin store"""
        if data_dir is None:
            # Use the app directory by default
            self.data_dir = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data"
            )
        else:
            self.data_dir = data_dir

        # Create the data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)

        # File paths
        self.credentials_file = os.path.join(self.data_dir, "credentials.json")
        self.settings_file = os.path.join(self.data_dir, "settings.json")
        self.status_file = os.path.join(self.data_dir, "status.json")

    def save_credentials(self, credentials: APICredentials) -> None:
        """Save API credentials to file"""
        credentials.last_updated = datetime.now().isoformat()
        with open(self.credentials_file, "w") as f:
            f.write(credentials.json())

    def get_credentials(self) -> APICredentials:
        """Get API credentials from file"""
        try:
            # Ensure data directory exists
            os.makedirs(self.data_dir, exist_ok=True)
            
            if not os.path.exists(self.credentials_file):
                # Return default credentials
                return APICredentials(
                    api_version=os.getenv("FACEBOOK_API_VERSION", "v17.0"),
                    ad_account_id=os.getenv("FACEBOOK_AD_ACCOUNT_ID", "act_123456789"),
                    access_token=os.getenv("FACEBOOK_ACCESS_TOKEN", "your_access_token_here"),
                )

            with open(self.credentials_file, "r") as f:
                data = json.load(f)

            return APICredentials(**data)
        except Exception as e:
            # Log the error and return default credentials
            logger.error(f"Error loading credentials: {str(e)}")
            return APICredentials(
                api_version=os.getenv("FACEBOOK_API_VERSION", "v17.0"),
                ad_account_id=os.getenv("FACEBOOK_AD_ACCOUNT_ID", "act_123456789"),
                access_token=os.getenv("FACEBOOK_ACCESS_TOKEN", "your_access_token_here"),
            )

    def save_settings(self, settings: AppSettings) -> None:
        """Save application settings to file"""
        settings.last_updated = datetime.now().isoformat()
        with open(self.settings_file, "w") as f:
            f.write(settings.json())

    def get_settings(self) -> AppSettings:
        """Get application settings from file"""
        try:
            # Ensure data directory exists
            os.makedirs(self.data_dir, exist_ok=True)
            
            if not os.path.exists(self.settings_file):
                # Return default settings with environment variables if available
                return AppSettings(
                    target_audience_min=int(os.getenv("TARGET_AUDIENCE_SIZE_MIN", 4000000)),
                    target_audience_max=int(os.getenv("TARGET_AUDIENCE_SIZE_MAX", 5000000))
                )

            with open(self.settings_file, "r") as f:
                data = json.load(f)

            return AppSettings(**data)
        except Exception as e:
            # Log the error and return default settings
            logger.error(f"Error loading settings: {str(e)}")
            return AppSettings(
                target_audience_min=int(os.getenv("TARGET_AUDIENCE_SIZE_MIN", 4000000)),
                target_audience_max=int(os.getenv("TARGET_AUDIENCE_SIZE_MAX", 5000000))
            )

    def save_status(self, status: APIStatus) -> None:
        """Save API status to file"""
        status.last_check = datetime.now().isoformat()
        with open(self.status_file, "w") as f:
            f.write(status.json())

    def get_status(self) -> APIStatus:
        """Get API status from file"""
        try:
            # Ensure data directory exists
            os.makedirs(self.data_dir, exist_ok=True)
            
            if not os.path.exists(self.status_file):
                # Return default status
                return APIStatus()

            with open(self.status_file, "r") as f:
                data = json.load(f)

            return APIStatus(**data)
        except Exception as e:
            # Log the error and return default status
            logger.error(f"Error loading status: {str(e)}")
            return APIStatus()
