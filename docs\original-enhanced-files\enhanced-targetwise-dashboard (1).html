<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TargetWise - Enhanced Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f0f2f5;
            color: #1a1a2e;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
            color: #2563eb;
            text-decoration: none;
            transition: transform 0.2s;
        }

        .logo:hover {
            transform: translateY(-1px);
        }

        .logo svg {
            width: 32px;
            height: 32px;
        }

        .nav-menu {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .nav-link {
            padding: 8px 16px;
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .nav-link:hover {
            color: #2563eb;
            background: rgba(37, 99, 235, 0.08);
        }

        .nav-link.active {
            color: #2563eb;
            background: rgba(37, 99, 235, 0.1);
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .theme-toggle {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: none;
            background: #f1f5f9;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .theme-toggle:hover {
            background: #e2e8f0;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-outline {
            background: transparent;
            color: #64748b;
            border: 2px solid #e2e8f0;
        }

        .btn-outline:hover {
            border-color: #cbd5e1;
            background: #f8fafc;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            color: white;
            box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.35);
        }

        /* Main Layout */
        .main-container {
            display: flex;
            max-width: 1400px;
            margin: 0 auto;
            gap: 24px;
            padding: 24px;
        }

        /* Sidebar */
        .sidebar {
            width: 260px;
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            height: fit-content;
            position: sticky;
            top: 100px;
        }

        .sidebar-title {
            font-size: 14px;
            font-weight: 600;
            color: #94a3b8;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 16px;
        }

        .sidebar-menu {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 10px;
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            transition: all 0.2s;
        }

        .sidebar-item:hover {
            background: #f1f5f9;
            color: #1e293b;
            transform: translateX(4px);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
            color: #2563eb;
        }

        .sidebar-icon {
            width: 20px;
            height: 20px;
            opacity: 0.7;
        }

        /* Content Area */
        .content {
            flex: 1;
        }

        /* Welcome Section */
        .welcome-section {
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            border-radius: 20px;
            padding: 48px;
            margin-bottom: 32px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(37, 99, 235, 0.2);
        }

        .welcome-content {
            position: relative;
            z-index: 2;
            max-width: 600px;
        }

        .welcome-title {
            font-size: 36px;
            font-weight: 700;
            color: white;
            margin-bottom: 16px;
            line-height: 1.2;
        }

        .welcome-subtitle {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 32px;
            line-height: 1.6;
        }

        .welcome-actions {
            display: flex;
            gap: 16px;
        }

        .btn-white {
            background: white;
            color: #2563eb;
            box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
        }

        .btn-white:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .btn-ghost {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn-ghost:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .welcome-graphic {
            position: absolute;
            right: -100px;
            top: 50%;
            transform: translateY(-50%);
            width: 400px;
            height: 400px;
            opacity: 0.1;
        }

        .circle-pattern {
            position: absolute;
            border-radius: 50%;
            border: 40px solid rgba(255, 255, 255, 0.1);
        }

        .circle-1 {
            width: 300px;
            height: 300px;
            top: -50px;
            right: -50px;
        }

        .circle-2 {
            width: 200px;
            height: 200px;
            bottom: -30px;
            right: 100px;
        }

        .circle-3 {
            width: 150px;
            height: 150px;
            top: 50px;
            right: 200px;
        }

        /* API Calls Counter */
        .api-counter {
            position: absolute;
            top: 24px;
            right: 24px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            padding: 8px 16px;
            border-radius: 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .api-counter-badge {
            background: rgba(255, 255, 255, 0.3);
            padding: 2px 8px;
            border-radius: 12px;
        }

        /* Tools Section */
        .tools-section {
            margin-bottom: 48px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
        }

        .view-all-link {
            color: #2563eb;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: gap 0.2s;
        }

        .view-all-link:hover {
            gap: 8px;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }

        .tool-card {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .tool-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #2563eb, #3b82f6);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.3s;
        }

        .tool-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
        }

        .tool-card:hover::before {
            transform: scaleX(1);
        }

        .tool-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 24px;
        }

        .tool-card:nth-child(2) .tool-icon {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        }

        .tool-card:nth-child(3) .tool-icon {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        }

        .tool-card:nth-child(4) .tool-icon {
            background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
        }

        .tool-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
        }

        .tool-description {
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 24px;
        }

        .tool-action {
            color: #2563eb;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: gap 0.2s;
        }

        .tool-action:hover {
            gap: 12px;
        }

        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: 32px;
            right: 32px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 8px 24px rgba(37, 99, 235, 0.3);
            cursor: pointer;
            transition: all 0.3s;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 32px rgba(37, 99, 235, 0.4);
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .sidebar {
                display: none;
            }
            
            .main-container {
                padding: 16px;
            }
            
            .welcome-section {
                padding: 32px;
            }
            
            .welcome-title {
                font-size: 28px;
            }
        }

        @media (max-width: 640px) {
            .header-content {
                padding: 12px 16px;
            }
            
            .nav-menu {
                display: none;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
            }
            
            .welcome-actions {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <a href="#" class="logo">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <circle cx="12" cy="12" r="6"></circle>
                    <circle cx="12" cy="12" r="2"></circle>
                </svg>
                TargetWise
            </a>
            
            <nav class="nav-menu">
                <a href="#" class="nav-link">Home</a>
                <a href="#" class="nav-link active">Micro-Tools</a>
                <a href="#" class="nav-link">Documentation</a>
                <a href="#" class="nav-link">Pricing</a>
                <a href="#" class="nav-link">Admin</a>
            </nav>
            
            <div class="header-actions">
                <button class="theme-toggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                </button>
                <a href="#" class="btn btn-outline">Log In</a>
                <a href="#" class="btn btn-primary">Sign Up Free</a>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <h3 class="sidebar-title">Micro-Tools</h3>
            <nav class="sidebar-menu">
                <a href="#" class="sidebar-item active">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="7" height="7"></rect>
                        <rect x="14" y="3" width="7" height="7"></rect>
                        <rect x="14" y="14" width="7" height="7"></rect>
                        <rect x="3" y="14" width="7" height="7"></rect>
                    </svg>
                    Dashboard
                </a>
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                    Interest Search
                </a>
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>
                    </svg>
                    Interest Suggestions
                </a>
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 3v18h18"></path>
                        <path d="m19 9-5 5-4-4-3 3"></path>
                    </svg>
                    Taxonomy Browser
                </a>
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"></path>
                    </svg>
                    Interest Pool
                </a>
            </nav>
        </aside>

        <!-- Content -->
        <main class="content">
            <!-- Welcome Section -->
            <section class="welcome-section">
                <div class="api-counter">
                    API Calls: <span class="api-counter-badge">200</span>
                </div>
                
                <div class="welcome-content">
                    <h1 class="welcome-title">Welcome to TargetWise Micro-Tools</h1>
                    <p class="welcome-subtitle">Powerful utilities to enhance your Facebook ad targeting research and optimization workflow.</p>
                    
                    <div class="welcome-actions">
                        <button class="btn btn-white">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polygon points="5 3 19 12 5 21 5 3"></polygon>
                            </svg>
                            Watch Tutorial
                        </button>
                        <button class="btn btn-ghost">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                <line x1="12" y1="17" x2="12.01" y2="17"></line>
                            </svg>
                            Get Help
                        </button>
                    </div>
                </div>
                
                <!-- Decorative circles -->
                <div class="circle-pattern circle-1"></div>
                <div class="circle-pattern circle-2"></div>
                <div class="circle-pattern circle-3"></div>
            </section>

            <!-- Available Tools -->
            <section class="tools-section">
                <div class="section-header">
                    <h2 class="section-title">Available Tools</h2>
                    <a href="#" class="view-all-link">
                        View All Tools
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </a>
                </div>

                <div class="tools-grid">
                    <!-- Interest Search -->
                    <div class="tool-card">
                        <div class="tool-icon">🔍</div>
                        <h3 class="tool-title">Interest Search</h3>
                        <p class="tool-description">Search for specific interests and audience segments for your Facebook ads.</p>
                        <a href="#" class="tool-action">
                            Open Tool
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>

                    <!-- Interest Suggestions -->
                    <div class="tool-card">
                        <div class="tool-icon">💡</div>
                        <h3 class="tool-title">Interest Suggestions</h3>
                        <p class="tool-description">Get AI-powered interest suggestions based on your products and audience.</p>
                        <a href="#" class="tool-action">
                            Open Tool
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>

                    <!-- Taxonomy Browser -->
                    <div class="tool-card">
                        <div class="tool-icon">🌳</div>
                        <h3 class="tool-title">Taxonomy Browser</h3>
                        <p class="tool-description">Explore Facebook's interest taxonomy and discover related interest categories.</p>
                        <a href="#" class="tool-action">
                            Open Tool
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>

                    <!-- Interest Pool -->
                    <div class="tool-card">
                        <div class="tool-icon">📁</div>
                        <h3 class="tool-title">Interest Pool</h3>
                        <p class="tool-description">Manage and export your collection of interests for use in Facebook ad campaigns.</p>
                        <a href="#" class="tool-action">
                            Open Tool
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Floating Action Button -->
    <button class="fab">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
    </button>

    <script>
        // Add interactivity
        document.querySelectorAll('.tool-card').forEach(card => {
            card.addEventListener('click', function(e) {
                if (!e.target.closest('.tool-action')) {
                    this.querySelector('.tool-action').click();
                }
            });
        });

        // Smooth scroll for sidebar items
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Theme toggle animation
        const themeToggle = document.querySelector('.theme-toggle');
        themeToggle.addEventListener('click', function() {
            this.style.transform = 'rotate(180deg)';
            setTimeout(() => {
                this.style.transform = 'rotate(0deg)';
            }, 300);
        });

        // API counter animation
        let apiCount = 200;
        setInterval(() => {
            apiCount += Math.floor(Math.random() * 3);
            document.querySelector('.api-counter-badge').textContent = apiCount;
        }, 5000);
    </script>
</body>
</html>