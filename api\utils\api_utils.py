# // DOC: docs/micro-tools/search-bulk-feature.md
"""
Utility functions for API requests and caching.
"""

import asyncio
import hashlib
import json
import logging
import time
from typing import Any, Callable, Dict, List, Optional, Tuple, TypeVar

import httpx

from api.utils.monitoring import api_monitor

logger = logging.getLogger(__name__)

T = TypeVar("T")


class ApiRequestHandler:
    """
    Utility class for handling API requests with caching, rate limiting, and error handling.
    """

    def __init__(
        self,
        base_url: str,
        access_token: str,
        redis_cache=None,
        local_cache: Dict[str, Any] = None,
        rate_limit_score: int = 0,
        api_rate_limit_score: int = 60,
        api_rate_limit_decay_seconds: int = 300,
        last_call_time: float = 0,
    ):
        """
        Initialize the API request handler.

        Args:
            base_url: Base URL for API requests
            access_token: API access token
            redis_cache: Redis cache instance
            local_cache: Dictionary for local caching
            rate_limit_score: Current rate limit score
            api_rate_limit_score: Maximum rate limit score
            api_rate_limit_decay_seconds: Time in seconds for rate limit score to decay
            last_call_time: Timestamp of the last API call
        """
        self.base_url = base_url
        self.access_token = access_token
        self.redis_cache = redis_cache
        self.local_cache = local_cache or {}
        self.rate_limit_score = rate_limit_score
        self.api_rate_limit_score = api_rate_limit_score
        self.api_rate_limit_decay_seconds = api_rate_limit_decay_seconds
        self.last_call_time = last_call_time
        self.retry_counts = {}

    async def make_api_request(
        self,
        endpoint: str,
        params: Dict[str, Any],
        cache_key: str,
        cache_ttl: int = 86400,  # 24 hours
        score_cost: int = 1,
        mock_data_func: Optional[Callable] = None,
        mock_data_args: Optional[List[Any]] = None,
        response_processor: Optional[Callable[[Dict[str, Any]], T]] = None,
    ) -> Tuple[bool, Optional[T], Optional[Dict[str, Any]]]:
        """
        Make an API request with caching, rate limiting, and error handling.

        Args:
            endpoint: API endpoint to call
            params: Parameters for the API request
            cache_key: Key for caching the response
            cache_ttl: Time-to-live for the cache entry in seconds
            score_cost: Cost of the API call in rate limit score points
            mock_data_func: Optional function to generate mock data
            mock_data_args: Arguments for the mock data function
            response_processor: Function to process the API response

        Returns:
            Tuple of (success, result, error)
        """
        # Start timing
        start_time = time.time()
        cache_hit = False


        # Check Redis cache first
        if self.redis_cache:
            cached = await self.redis_cache.get(cache_key)
            if cached:
                logger.info(f"Using Redis cached data for key: {cache_key}")
                cached_data = json.loads(cached)

                # Record the API call (Redis cache hit)
                response_time = time.time() - start_time
                cache_hit = True
                api_monitor.record_api_call(
                    endpoint=endpoint,
                    success=True,
                    response_time=response_time,
                    cache_hit=True,
                )

                if response_processor:
                    return True, response_processor(cached_data), None
                return True, cached_data, None

        # Check local cache
        if cache_key in self.local_cache:
            logger.info(f"Using local cached data for key: {cache_key}")

            # Record the API call (local cache hit)
            response_time = time.time() - start_time
            cache_hit = True
            api_monitor.record_api_call(
                endpoint=endpoint,
                success=True,
                response_time=response_time,
                cache_hit=True,
            )

            return True, self.local_cache[cache_key], None

        # Respect rate limits
        await self._handle_rate_limit()

        # Make the API request
        url = f"{self.base_url}/{endpoint}"

        try:
            logger.info(f"Calling API endpoint: {endpoint}")
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)

            # Calculate response time
            response_time = time.time() - start_time

            # Parse usage headers
            self._parse_x_ad_account_usage(response)

            # Increment rate limit score
            self.rate_limit_score += score_cost

            if response.status_code == 200:
                data = response.json()

                # Process the response
                result = data
                if response_processor:
                    result = response_processor(data)

                # Cache the result
                if self.redis_cache:
                    if isinstance(result, dict) or isinstance(result, list):
                        await self.redis_cache.set(
                            cache_key, json.dumps(result), cache_ttl
                        )
                    else:
                        # For custom objects, use their __dict__ or model_dump_json() if available
                        if hasattr(result, "model_dump_json"):
                            await self.redis_cache.set(
                                cache_key, result.model_dump_json(), cache_ttl
                            )
                        elif hasattr(result, "__dict__"):
                            await self.redis_cache.set(
                                cache_key, json.dumps(result.__dict__), cache_ttl
                            )

                self.local_cache[cache_key] = result

                # Record the successful API call
                api_monitor.record_api_call(
                    endpoint=endpoint,
                    success=True,
                    response_time=response_time,
                    cache_hit=False,
                )

                return True, result, None
            else:
                logger.error(
                    f"API error for {endpoint}: {response.status_code} - {response.text}"
                )

                # Handle API errors
                error_data = None
                try:
                    error_data = response.json()
                    await self._handle_api_error(error_data)
                except:
                    pass

                # Record the failed API call
                api_monitor.record_api_call(
                    endpoint=endpoint,
                    success=False,
                    response_time=response_time,
                    cache_hit=False,
                    error=error_data
                    or {"error": {"message": f"HTTP error {response.status_code}"}},
                )

                # If there's an error and mock data function is provided, use the mock
                if mock_data_func:
                    mock_result = mock_data_func(*(mock_data_args or []))
                    logger.info(f"Using mock data for {endpoint} due to API error")

                    api_monitor.record_api_call(
                        endpoint=endpoint,
                        success=True,
                        response_time=response_time,
                        cache_hit=True,
                    )

                    return True, mock_result, None

                return (
                    False,
                    None,
                    error_data
                    or {"error": {"message": f"HTTP error {response.status_code}"}},
                )

        except Exception as e:
            logger.error(f"Exception while calling {endpoint}: {str(e)}")

            # Calculate response time
            response_time = time.time() - start_time

            # Record the failed API call
            api_monitor.record_api_call(
                endpoint=endpoint,
                success=False,
                response_time=response_time,
                cache_hit=False,
                error={"error": {"message": str(e)}},
            )

            # Wait a bit before continuing
            await asyncio.sleep(1)

            # If there's an exception and mock data function is provided, use it
            if mock_data_func:
                mock_result = mock_data_func(*(mock_data_args or []))
                logger.info(f"Using mock data for {endpoint} due to exception")

                api_monitor.record_api_call(
                    endpoint=endpoint,
                    success=True,
                    response_time=response_time,
                    cache_hit=True,
                )

                return True, mock_result, None

            return False, None, {"error": {"message": str(e)}}

    async def _handle_rate_limit(self):
        """
        Decays the rate limit score over time and sleeps if the score exceeds the max allowed.
        """
        now = time.time()
        elapsed = now - self.last_call_time
        decay = self.api_rate_limit_decay_seconds
        max_score = self.api_rate_limit_score

        # Decay the score based on elapsed time
        if elapsed > 0:
            decay_amount = int((elapsed / decay) * max_score)
            self.rate_limit_score = max(0, self.rate_limit_score - decay_amount)

        self.last_call_time = now

        # If over the limit, sleep until reset
        if self.rate_limit_score >= max_score:
            sleep_time = decay - elapsed if elapsed < decay else 0
            if sleep_time > 0:
                logger.warning(
                    f"API rate limit reached. Sleeping for {sleep_time:.1f} seconds."
                )
                await asyncio.sleep(sleep_time)
            self.rate_limit_score = 0

    async def _handle_api_error(self, error_data, retry_count=None):
        """
        Handle API errors, particularly rate limiting with exponential backoff.

        Args:
            error_data: The error data from the API response
            retry_count: The current retry count for exponential backoff (if None, use the internal tracking)
        """
        if "error" in error_data:
            error = error_data["error"]
            code = error.get("code", 0)
            message = error.get("message", "Unknown error")
            error_key = f"{code}:{message[:20]}"  # Use a key based on error code and start of message

            # If retry_count is None, use the internal tracking
            if retry_count is None:
                if error_key not in self.retry_counts:
                    self.retry_counts[error_key] = 0
                else:
                    self.retry_counts[error_key] += 1
                retry_count = self.retry_counts[error_key]

            # Handle rate limit errors with exponential backoff
            if (
                code == 17
                or code == 4
                or code == 80004
                or "rate limit" in message.lower()
            ):
                # Calculate backoff time: 1s, 2s, 4s, 8s, 16s, 32s
                backoff_time = min(2**retry_count, 32)
                logger.warning(
                    f"Rate limit hit: {code} - {message}. Backing off for {backoff_time}s (retry {retry_count+1})"
                )
                await asyncio.sleep(backoff_time)
            else:
                logger.error(f"API error: {code} - {message}")
                # For other errors, wait a bit but not as long
                await asyncio.sleep(5)

    def _parse_x_ad_account_usage(self, response):
        """
        Parse the X-Ad-Account-Usage header from the response.

        Args:
            response: The API response

        Returns:
            The parsed usage data or None if not available
        """
        usage = None
        try:
            if (
                response
                and hasattr(response, "headers")
                and "x-ad-account-usage" in response.headers
            ):
                usage = response.headers["x-ad-account-usage"]
                logger.info(f"X-Ad-Account-Usage: {usage}")
        except Exception as e:
            logger.warning(f"Failed to parse X-Ad-Account-Usage header: {e}")
        return usage

    @staticmethod
    def hash_spec(spec: Dict[str, Any]) -> str:
        """
        Create a hash of a targeting specification for caching.

        Args:
            spec: The targeting specification

        Returns:
            A hash string
        """
        spec_str = json.dumps(spec, sort_keys=True)
        return hashlib.sha1(spec_str.encode()).hexdigest()
