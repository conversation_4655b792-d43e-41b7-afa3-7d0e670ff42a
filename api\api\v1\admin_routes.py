# // DOC: docs/micro-tools/search-bulk-feature.md
import logging
import os
import secrets
from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, Form, HTTPException, Request, status
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from pathlib import Path

from api.models.admin import AdminStore, APICredentials, APIStatus, AppSettings
from api.services import ReachEstimationService, TaxonomyService

logger = logging.getLogger(__name__)

router = APIRouter()
__BASE_APP_DIR = Path(__file__).resolve().parents[2]
templates = Jinja2Templates(directory=str(__BASE_APP_DIR / "templates"))
admin_store = AdminStore()





@router.get("/admin", response_class=HTMLResponse)
async def admin_dashboard(
    request: Request,
    message: Optional[str] = None,
    message_type: Optional[str] = None,
):
    """Admin dashboard page"""
    # Render the admin dashboard, catching errors during template loading/rendering
    try:
        # Get credentials, settings, and status
        credentials = admin_store.get_credentials()
        settings = admin_store.get_settings()
        api_status = admin_store.get_status()

        # Format the last check time
        last_check = "Never"
        if api_status and api_status.last_check:
            try:
                last_check_dt = datetime.fromisoformat(api_status.last_check)
                last_check = last_check_dt.strftime("%Y-%m-%d %H:%M:%S")
            except Exception as err:
                logger.warning(f"Error formatting last check time: {err}")
                last_check = "Error"

        # Format the rate limit and status
        rate_limit = getattr(api_status, 'rate_limit', None) or "Unknown"
        api_status_value = getattr(api_status, 'status', 'unknown')

        context = {
            "request": request,
            "now": datetime.now(),
            "credentials": credentials or {},
            "settings": {
                "target_audience_min": (getattr(settings, 'target_audience_min', 4000000) // 1000000),
                "target_audience_max": (getattr(settings, 'target_audience_max', 5000000) // 1000000),
            },
            "api_status": api_status_value,
            "last_check": last_check,
            "rate_limit": rate_limit,
            "message": message,
            "message_type": message_type,
        }
        # Pre-render template to catch errors early
        html_content = templates.env.get_template("admin.html").render(**context)
        return HTMLResponse(content=html_content)
    except Exception as err:
        logger.error(f"Error rendering admin dashboard: {err}")
        # Fallback: show a simple error page
        error_html = (
            f"<html><body>"
            f"<h1>Admin Dashboard Error</h1>"
            f"<p>{str(err)}</p>"
            f"</body></html>"
        )
        return HTMLResponse(content=error_html, status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.post("/admin/save-credentials")
async def save_credentials(
    request: Request,
    api_version: str = Form(...),
    ad_account_id: str = Form(...),
    access_token: str = Form(...),
):
    """Save API credentials"""
    # Create credentials object
    credentials = APICredentials(
        api_version=api_version, ad_account_id=ad_account_id, access_token=access_token
    )

    # Save credentials
    admin_store.save_credentials(credentials)

    # Update environment variables
    os.environ["FACEBOOK_API_VERSION"] = api_version
    os.environ["FACEBOOK_AD_ACCOUNT_ID"] = ad_account_id
    os.environ["FACEBOOK_ACCESS_TOKEN"] = access_token

    # Redirect back to admin dashboard with success message
    return RedirectResponse(
        url="/admin?message=Credentials+saved+successfully&message_type=success",
        status_code=status.HTTP_303_SEE_OTHER,
    )


@router.post("/admin/save-settings")
async def save_settings(
    request: Request,
    target_audience_min: int = Form(...),
    target_audience_max: int = Form(...),
):
    """Save application settings"""
    # Create settings object
    settings = AppSettings(
        target_audience_min=target_audience_min * 1000000,  # Convert from millions
        target_audience_max=target_audience_max * 1000000,  # Convert from millions
    )

    # Save settings
    admin_store.save_settings(settings)

    # Update environment variables
    os.environ["TARGET_AUDIENCE_SIZE_MIN"] = str(settings.target_audience_min)
    os.environ["TARGET_AUDIENCE_SIZE_MAX"] = str(settings.target_audience_max)

    # Redirect back to admin dashboard with success message
    return RedirectResponse(
        url="/admin?message=Settings+saved+successfully&message_type=success",
        status_code=status.HTTP_303_SEE_OTHER,
    )


@router.post("/admin/test-connection")
async def test_connection(
    request: Request,
    credentials: Dict[str, Any],
    silent: bool = False,
):
    """Test Facebook API connection"""
    # Create a temporary ReachEstimationService with the provided credentials
    fb_service = ReachEstimationService(
        api_version=credentials.get("api_version"),
        ad_account_id=credentials.get("ad_account_id"),
        access_token=credentials.get("access_token"),
    )

    # Test the connection
    try:
        # Try to fetch a simple piece of data from the API
        result = await fb_service.test_connection()

        if result.get("success"):
            # Update the API status
            api_status = APIStatus(
                status="connected", rate_limit=result.get("rate_limit", "Unknown")
            )
            admin_store.save_status(api_status)

            return JSONResponse(content={"success": True, "silent": silent})
        else:
            # Update the API status
            api_status = APIStatus(
                status="disconnected", error=result.get("error", "Unknown error")
            )
            admin_store.save_status(api_status)

            return JSONResponse(
                content={
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "silent": silent,
                }
            )

    except Exception as e:
        # Update the API status
        api_status = APIStatus(status="disconnected", error=str(e))
        admin_store.save_status(api_status)

        return JSONResponse(
            content={"success": False, "error": str(e), "silent": silent}
        )


@router.get("/admin/api-status")
async def get_api_status(request: Request):
    """Get API status"""
    # Get the current API status
    api_status = admin_store.get_status()

    # Format the last check time
    last_check = "Never"
    if api_status.last_check:
        try:
            last_check_dt = datetime.fromisoformat(api_status.last_check)
            last_check = last_check_dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            pass

    # Format the rate limit
    rate_limit = api_status.rate_limit or "Unknown"

    return JSONResponse(
        content={
            "status": api_status.status,
            "last_check": last_check,
            "rate_limit": rate_limit,
            "error": api_status.error,
        }
    )


@router.get("/admin/browse-targeting")
async def browse_targeting_categories(
    request: Request,
    category_type: str = "adtargetingcategory",
    targeting_class: str = "demographics",
):
    """Browse targeting categories from Facebook API"""
    # Get credentials
    credentials = admin_store.get_credentials()

    # Create a TaxonomyService with the stored credentials
    fb_service = TaxonomyService(
        api_version=credentials.api_version,
        ad_account_id=credentials.ad_account_id,
        access_token=credentials.access_token,
    )

    try:
        # Call the browse_targeting_categories method
        categories = await fb_service.browse_targeting_categories(
            category_type, targeting_class
        )

        return JSONResponse(content={"success": True, "data": categories})
    except Exception as e:
        return JSONResponse(content={"success": False, "error": str(e)})
