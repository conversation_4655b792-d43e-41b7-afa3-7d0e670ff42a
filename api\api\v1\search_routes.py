# // DOC: docs/micro-tools/search-bulk-feature.md
import logging
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Request

from api.models.admin import AdminStore
from api.schemas import InterestItem
from api.services import (
    InterestSearchService,
    SuggestionService,
    TaxonomyService,
)
from api.utils.notifications import (
    notify_debug, notify_error, notify_info, notify_success, notify_warning
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/targeting/taxonomy")
async def get_interest_taxonomy(
    request: Request,
    parent_id: str = Query(None, description="Parent category ID (optional)"),
    limit_type: str = Query(
        "interests", description="Type of results to retrieve (default: interests)"
    ),
    locale: str = Query(
        "en_US", description="Locale for names/descriptions (default: en_US)"
    ),
):
    """Browse Facebook interest taxonomy using /targetingbrowse endpoint."""
    try:
        notify_info(
            f"Fetching interest taxonomy - Parent ID: {parent_id}, Type: {limit_type}",
            context="api",
            data={"endpoint": "/targeting/taxonomy", "client": request.client.host}
        )

        admin_store = AdminStore()
        credentials = admin_store.get_credentials()

        fb_service = TaxonomyService(
            api_version=credentials.api_version,
            ad_account_id=credentials.ad_account_id,
            access_token=credentials.access_token,
        )

        taxonomy = await fb_service.fetch_interest_taxonomy(
            parent_id=parent_id, limit_type=limit_type, locale=locale
        )

        notify_success(
            f"Successfully fetched {len(taxonomy)} taxonomy items",
            context="api",
            data={"count": len(taxonomy), "parent_id": parent_id}
        )

        return {"data": taxonomy}
    except Exception as e:
        error_msg = f"Error fetching interest taxonomy: {str(e)}"
        notify_error(
            error_msg,
            context="api",
            data={"error": str(e), "parent_id": parent_id, "limit_type": limit_type}
        )
        raise HTTPException(status_code=500, detail=error_msg)


@router.get("/targeting/search")
async def search_interests(
    request: Request,
    q: str = Query(..., description="Search query"),
    type: str = Query("adinterest", description="Type of targeting entity"),
    limit: int = Query(1000, description="Maximum number of results to return"),
):
    """Search for interests using Facebook's targeting search."""
    try:
        # Log search attempt
        notify_info(
            f"Searching for interests - Query: '{q}', Type: {type}, Limit: {limit}",
            context="api/search",
            data={
                "query": q,
                "type": type,
                "limit": limit,
                "client": request.client.host
            }
        )

        admin_store = AdminStore()
        credentials = admin_store.get_credentials()

        # Log API version and account info
        notify_debug(
            f"Using API version: {credentials.api_version}, Ad Account: {credentials.ad_account_id}",
            context="api/search"
        )

        fb_service = InterestSearchService(
            api_version=credentials.api_version,
            ad_account_id=credentials.ad_account_id,
            access_token=credentials.access_token,
        )

        # Execute search
        results = await fb_service.search_interests(q, type, limit)

        # Log results
        notify_success(
            f"Found {len(results)} interests for query: '{q}'",
            context="api/search",
            data={
                "query": q,
                "result_count": len(results),
                "type": type
            }
        )

        return {"data": results}
    except HTTPException as he:
        # Re-raise HTTP exceptions as they are
        raise he
    except Exception as e:
        error_msg = f"Error searching interests: {str(e)}"
        logger.error(error_msg)
        notify_error(
            error_msg,
            context="api/search",
            data={
                "query": q,
                "type": type,
                "error": str(e)
            }
        )
        error_detail = {
            "message": error_msg,
            "query": q,
            "type": type,
            "limit": limit,
            "timestamp": datetime.now().isoformat(),
        }
        raise HTTPException(status_code=500, detail=error_detail)


@router.get("/targeting/suggestions")
async def get_interest_suggestions(
    interest_id: str = Query(..., description="Interest ID to get suggestions for"),
    limit: int = Query(1000, description="Maximum number of results to return"),
):
    """Get suggestions for related interests based on a seed interest."""
    try:
        admin_store = AdminStore()
        credentials = admin_store.get_credentials()

        fb_service = SuggestionService(
            api_version=credentials.api_version,
            ad_account_id=credentials.ad_account_id,
            access_token=credentials.access_token,
        )

        seed_interest = InterestItem(id=interest_id, name="Seed Interest")
        suggestions = await fb_service.fetch_suggestions([seed_interest], limit=limit)
        return {
            "success": True,
            "data": [suggestion.model_dump() for suggestion in suggestions],
        }
    except Exception as e:
        logger.error(f"Error getting interest suggestions: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error getting interest suggestions: {str(e)}"
        )


@router.get("/targeting/browse")
async def browse_targeting_categories(
    category_type: str = Query(
        "adtargetingcategory", description="Type of targeting category to browse"
    ),
    targeting_class: str = Query(
        "demographics",
        description="Class of targeting to browse (demographics, interests, behaviors, etc.)",
    ),
):
    """Browse targeting categories from Facebook API."""
    try:
        admin_store = AdminStore()
        credentials = admin_store.get_credentials()

        fb_service = TaxonomyService(
            api_version=credentials.api_version,
            ad_account_id=credentials.ad_account_id,
            access_token=credentials.access_token,
        )

        categories = await fb_service.browse_targeting_categories(
            category_type, targeting_class
        )
        return {"success": True, "data": categories}
    except Exception as e:
        logger.error(f"Error browsing targeting categories: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error browsing targeting categories: {str(e)}"
        )

