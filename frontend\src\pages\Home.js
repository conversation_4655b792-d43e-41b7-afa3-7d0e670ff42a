/**
 * Home Page - Landing page with targeting sheet builder
 * Based on targetwise-main-enhanced.html
 */

import { Header, initHeader } from '../components/common/Header.js';

export function HomePage() {
  return `
    ${Header({ currentPage: 'home' })}
    
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">Welcome to TargetWise</h1>
        <p class="hero-subtitle">Build powerful 12-column Algorithmic Targeting Sheets using the Meta Marketing API for optimized Facebook ad campaigns</p>
        <div class="hero-features">
          <div class="hero-feature">
            <div class="feature-icon">✓</div>
            <span>AI-Powered Targeting</span>
          </div>
          <div class="hero-feature">
            <div class="feature-icon">✓</div>
            <span>Meta API Integration</span>
          </div>
          <div class="hero-feature">
            <div class="feature-icon">✓</div>
            <span>Export Ready</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
      <div class="container">
        <!-- Info Cards -->
        <div class="grid grid-cols-auto gap-6 mb-10">
          <div class="card">
            <div class="bg-gradient-primary rounded-xl p-4 mb-5 w-12 h-12 flex items-center justify-center text-white text-2xl">🎯</div>
            <h3 class="text-xl font-semibold text-gray-800 mb-3">Smart Targeting</h3>
            <p class="text-gray-600">Generate precise audience segments using advanced algorithms and Meta's comprehensive interest database</p>
          </div>
          <div class="card">
            <div class="bg-gradient-success rounded-xl p-4 mb-5 w-12 h-12 flex items-center justify-center text-white text-2xl">📊</div>
            <h3 class="text-xl font-semibold text-gray-800 mb-3">12-Column Format</h3>
            <p class="text-gray-600">Industry-standard targeting sheet format optimized for Facebook Ads Manager import and campaign setup</p>
          </div>
          <div class="card">
            <div class="bg-gradient-warning rounded-xl p-4 mb-5 w-12 h-12 flex items-center justify-center text-white text-2xl">⚡</div>
            <h3 class="text-xl font-semibold text-gray-800 mb-3">Instant Results</h3>
            <p class="text-gray-600">Get your targeting sheet in seconds, ready to use in your Facebook advertising campaigns</p>
          </div>
        </div>

        <!-- Builder Card -->
        <div class="card">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 flex items-center justify-center gap-4 mb-4">
              <div class="bg-gradient-primary rounded-xl p-3 text-white text-2xl">🚀</div>
              Build Your Targeting Sheet
            </h2>
            <p class="text-gray-600">Upload your interests or enter them manually to generate a comprehensive targeting strategy</p>
          </div>

          <div id="successMessage" class="success-message hidden">
            <div class="success-icon">✓</div>
            <span class="success-text">Targeting sheet generated successfully!</span>
          </div>

          <form id="targetingForm" class="space-y-8">
            <!-- Upload Section -->
            <div class="form-section">
              <h3 class="text-lg font-semibold text-gray-800 flex items-center gap-3 mb-4">
                <div class="bg-blue-50 rounded-lg p-2 text-primary">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="17 8 12 3 7 8"></polyline>
                    <line x1="12" y1="3" x2="12" y2="15"></line>
                  </svg>
                </div>
                Upload Interests CSV (Optional)
              </h3>
              
              <div class="file-upload-area border-2 border-dashed border-gray-200 rounded-xl p-8 text-center transition cursor-pointer hover:border-primary hover:bg-blue-50" id="fileUploadArea">
                <input type="file" id="fileInput" class="hidden" accept=".csv">
                <div class="bg-blue-100 rounded-full p-4 w-16 h-16 flex items-center justify-center mx-auto mb-4 text-primary">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                </div>
                <p class="text-gray-600 mb-3">Click to upload or drag and drop</p>
                <p class="text-gray-400 text-sm">CSV files only (max 10MB)</p>
              </div>
              
              <a href="#" class="inline-flex items-center gap-2 text-primary hover:text-primary-hover transition mt-4">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                Download Sample CSV
              </a>
            </div>

            <!-- Manual Input Section -->
            <div class="form-section">
              <h3 class="text-lg font-semibold text-gray-800 flex items-center gap-3 mb-4">
                <div class="bg-blue-50 rounded-lg p-2 text-primary">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                  </svg>
                </div>
                Seed Interests (Optional if CSV uploaded)
              </h3>
              
              <div class="relative">
                <textarea 
                  class="form-textarea w-full" 
                  placeholder="Enter comma-separated interests (e.g., fitness, yoga, meditation, wellness, health)..."
                  id="seedInterests"
                ></textarea>
                <span class="absolute bottom-3 right-3 text-gray-400 text-sm" id="charCounter">0 interests</span>
              </div>
            </div>

            <!-- Settings Section -->
            <div class="form-section">
              <h3 class="text-lg font-semibold text-gray-800 flex items-center gap-3 mb-4">
                <div class="bg-blue-50 rounded-lg p-2 text-primary">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="3"></circle>
                    <path d="M12 1v6m0 6v6m-9-9h6m6 0h6m-11.364 6.364l4.243-4.243m0 0l4.242-4.242m-4.242 4.242l-4.243-4.243m4.243 4.243l4.242 4.242"></path>
                  </svg>
                </div>
                Targeting Parameters
              </h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                  <label class="form-label">Country Code</label>
                  <input type="text" class="form-input" value="US" placeholder="e.g., US" id="countryCode">
                </div>
                <div class="form-group">
                  <label class="form-label">Age Range</label>
                  <div class="flex items-center gap-3">
                    <input type="number" class="form-input" value="18" placeholder="Min" min="13" max="65" id="minAge">
                    <span class="text-gray-400">to</span>
                    <input type="number" class="form-input" value="65" placeholder="Max" min="13" max="65" id="maxAge">
                  </div>
                </div>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="text-center">
              <button type="submit" class="btn btn-primary btn-lg">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M22 2L11 13"></path>
                  <path d="M22 2L15 22L11 13L2 9L22 2Z"></path>
                </svg>
                Build Targeting Sheet
              </button>
            </div>
          </form>
        </div>
      </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay hidden">
      <div class="loading-content">
        <div class="spinner mx-auto mb-5"></div>
        <p class="loading-text">Building Your Targeting Sheet</p>
        <p class="loading-subtext">This may take a few seconds...</p>
      </div>
    </div>
  `;
}

// Initialize home page functionality
export function initHomePage() {
  initHeader();
  
  // File upload functionality
  const fileInput = document.getElementById('fileInput');
  const fileUploadArea = document.getElementById('fileUploadArea');
  const seedInterests = document.getElementById('seedInterests');
  const charCounter = document.getElementById('charCounter');

  if (fileUploadArea && fileInput) {
    fileUploadArea.addEventListener('click', () => fileInput.click());

    fileUploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      fileUploadArea.classList.add('border-primary', 'bg-blue-50');
    });

    fileUploadArea.addEventListener('dragleave', () => {
      fileUploadArea.classList.remove('border-primary', 'bg-blue-50');
    });

    fileUploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      fileUploadArea.classList.remove('border-primary', 'bg-blue-50');
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        fileInput.files = files;
        handleFileSelect();
      }
    });

    fileInput.addEventListener('change', handleFileSelect);
  }

  function handleFileSelect() {
    const file = fileInput.files[0];
    if (file) {
      const uploadText = fileUploadArea.querySelector('p');
      uploadText.textContent = `Selected: ${file.name}`;
      fileUploadArea.classList.add('border-primary', 'bg-blue-50');
    }
  }

  // Interest counter
  if (seedInterests && charCounter) {
    seedInterests.addEventListener('input', () => {
      const interests = seedInterests.value.split(',').filter(i => i.trim()).length;
      charCounter.textContent = `${interests} interests`;
    });
  }

  // Form submission
  const form = document.getElementById('targetingForm');
  if (form) {
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      
      // Validation
      const hasFile = fileInput.files.length > 0;
      const hasInterests = seedInterests.value.trim().length > 0;
      
      if (!hasFile && !hasInterests) {
        alert('Please upload a CSV file or enter seed interests');
        return;
      }
      
      // Show loading
      document.getElementById('loadingOverlay').classList.remove('hidden');
      
      // Simulate API call
      setTimeout(() => {
        document.getElementById('loadingOverlay').classList.add('hidden');
        document.getElementById('successMessage').classList.remove('hidden');
        
        // Reset form
        setTimeout(() => {
          document.getElementById('successMessage').classList.add('hidden');
        }, 5000);
      }, 3000);
    });
  }

  // Sample CSV download
  const sampleLink = document.querySelector('a[href="#"]');
  if (sampleLink) {
    sampleLink.addEventListener('click', (e) => {
      e.preventDefault();
      // In real app, this would download a file
      alert('Sample CSV download started');
    });
  }
}

export default HomePage;