/**
 * Main Application Entry Point
 * Handles routing and page initialization
 */

import { HomePage, initHomePage } from './pages/Home.js';
import { InterestSearchPage, initInterestSearchPage } from './pages/InterestSearch.js';
import { TargetWiseMainPage, initTargetWiseMainPage } from './pages/TargetWiseMain.js';
import { DashboardPage, initDashboardPage } from './pages/Dashboard.js';
import { InterestPoolPage, initInterestPoolPage } from './pages/InterestPool.js';
import { InterestSuggestionsPage, initInterestSuggestionsPage } from './pages/InterestSuggestions.js';
import { InterestSearchEnhancedPage, initInterestSearchEnhancedPage } from './pages/InterestSearchEnhanced.js';

// Simple client-side router
class Router {
  constructor() {
    this.routes = new Map();
    this.currentRoute = null;
    
    // Listen for popstate events (back/forward browser buttons)
    window.addEventListener('popstate', () => this.handleRoute());
    
    // Listen for navigation clicks
    document.addEventListener('click', (e) => {
      if (e.target.matches('a[href^="/"]') || e.target.closest('a[href^="/"]')) {
        e.preventDefault();
        const link = e.target.matches('a') ? e.target : e.target.closest('a');
        this.navigate(link.href);
      }
    });
  }

  // Register a route
  addRoute(path, component, init) {
    this.routes.set(path, { component, init });
  }

  // Navigate to a route
  navigate(url) {
    const path = new URL(url, window.location.origin).pathname;
    window.history.pushState({}, '', url);
    this.handleRoute(path);
  }

  // Handle current route
  handleRoute(path = window.location.pathname) {
    const route = this.routes.get(path) || this.routes.get('/404');
    
    if (route) {
      this.currentRoute = path;
      
      // Render the component
      document.getElementById('app').innerHTML = route.component();
      
      // Initialize the page
      if (route.init) {
        // Use setTimeout to ensure DOM is updated
        setTimeout(route.init, 0);
      }
    }
  }

  // Start the router
  start() {
    this.handleRoute();
  }
}

// Initialize the application
function initApp() {
  // Create router instance
  const router = new Router();

  // Register routes
  router.addRoute('/', TargetWiseMainPage, initTargetWiseMainPage);
  router.addRoute('/home', HomePage, initHomePage);
  router.addRoute('/dashboard', DashboardPage, initDashboardPage);
  router.addRoute('/search', InterestSearchEnhancedPage, initInterestSearchEnhancedPage);
  router.addRoute('/pool', InterestPoolPage, initInterestPoolPage);
  router.addRoute('/suggestions', InterestSuggestionsPage, initInterestSuggestionsPage);
  router.addRoute('/micro-tools/search', InterestSearchPage, initInterestSearchPage);
  
  // Add 404 route
  router.addRoute('/404', () => `
    <div class="min-h-screen flex items-center justify-center bg-gray-50">
      <div class="text-center">
        <h1 class="text-6xl font-bold text-gray-300 mb-4">404</h1>
        <p class="text-xl text-gray-600 mb-6">Page not found</p>
        <a href="/" class="btn btn-primary">Go Home</a>
      </div>
    </div>
  `);

  // Start the router
  router.start();
  
  // Make router globally available for debugging
  window.router = router;
}

// Application state management
class AppState {
  constructor() {
    this.state = {
      user: null,
      cart: JSON.parse(localStorage.getItem('cartItems') || '[]'),
      searchHistory: JSON.parse(localStorage.getItem('searchHistory') || '[]'),
      favorites: JSON.parse(localStorage.getItem('favorites') || '[]')
    };
    
    this.listeners = new Map();
  }

  // Get state
  get(key) {
    return this.state[key];
  }

  // Set state
  set(key, value) {
    const oldValue = this.state[key];
    this.state[key] = value;
    
    // Persist certain state to localStorage
    if (['cart', 'searchHistory', 'favorites'].includes(key)) {
      localStorage.setItem(key === 'cart' ? 'cartItems' : key, JSON.stringify(value));
    }
    
    // Notify listeners
    if (this.listeners.has(key)) {
      this.listeners.get(key).forEach(callback => {
        callback(value, oldValue);
      });
    }
  }

  // Subscribe to state changes
  subscribe(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, []);
    }
    this.listeners.get(key).push(callback);
    
    // Return unsubscribe function
    return () => {
      const callbacks = this.listeners.get(key);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    };
  }
}

// Create global app state
window.appState = new AppState();

// Global utility functions
window.utils = {
  // Format numbers for display
  formatNumber(num) {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num?.toString() || '0';
  },

  // Format time ago
  formatTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now - time;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)} hr ago`;
    return `${Math.floor(diffMins / 1440)} day ago`;
  },

  // Debounce function
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  // Show toast notification
  showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 text-white ${
      type === 'success' ? 'bg-green-500' :
      type === 'error' ? 'bg-red-500' :
      type === 'warning' ? 'bg-yellow-500' :
      'bg-blue-500'
    }`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
      toast.style.transform = 'translateX(0)';
    }, 10);
    
    // Remove after 3 seconds
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => toast.remove(), 300);
    }, 3000);
  }
};

// Global error handler
window.addEventListener('error', (e) => {
  console.error('Global error:', e.error);
  window.utils.showToast('An unexpected error occurred', 'error');
});

// Global promise rejection handler
window.addEventListener('unhandledrejection', (e) => {
  console.error('Unhandled promise rejection:', e.reason);
  window.utils.showToast('Network error occurred', 'error');
});

// Initialize the app when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp);
} else {
  initApp();
}

export { Router, AppState, initApp };