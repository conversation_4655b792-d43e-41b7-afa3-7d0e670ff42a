/* TargetWise Design System Tokens
   ================================
   Design tokens for consistent styling across all components
   Based on the TargetWise Design System documentation
*/

:root {
  /* Typography */
  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* Font Sizes */
  --font-size-display: 36px;
  --font-size-h1: 32px;
  --font-size-h2: 28px;
  --font-size-h3: 20px;
  --font-size-h4: 18px;
  --font-size-body: 16px;
  --font-size-small: 14px;
  --font-size-caption: 13px;
  
  /* Font Weights */
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Line Heights */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Primary Colors */
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-primary-light: rgba(37, 99, 235, 0.1);
  --color-primary-gradient: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  
  /* Secondary Colors */
  --color-success: #10b981;
  --color-success-gradient: linear-gradient(135deg, #10b981 0%, #34d399 100%);
  --color-warning: #f59e0b;
  --color-warning-gradient: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
  --color-danger: #ef4444;
  --color-danger-gradient: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
  --color-purple: #8b5cf6;
  --color-purple-gradient: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
  
  /* Neutral Colors */
  --color-gray-900: #1a1a2e;
  --color-gray-800: #1e293b;
  --color-gray-700: #334155;
  --color-gray-600: #475569;
  --color-gray-500: #64748b;
  --color-gray-400: #94a3b8;
  --color-gray-300: #cbd5e1;
  --color-gray-200: #e2e8f0;
  --color-gray-100: #f1f5f9;
  --color-gray-50: #f8fafc;
  --color-white: #ffffff;
  
  /* Background Gradients */
  --bg-gradient-blue: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  --bg-gradient-green: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  --bg-gradient-yellow: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  --bg-gradient-pink: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
  --bg-gradient-purple: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
  
  /* Spacing (8px grid) */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-base: 16px;
  --spacing-lg: 20px;
  --spacing-xl: 24px;
  --spacing-2xl: 32px;
  --spacing-3xl: 48px;
  
  /* Border Radius */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 10px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-round: 20px;
  --radius-full: 9999px;
  --radius-circle: 50%;
  
  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 14px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 12px 32px rgba(0, 0, 0, 0.16);
  --shadow-primary: 0 4px 14px rgba(37, 99, 235, 0.25);
  --shadow-primary-hover: 0 6px 20px rgba(37, 99, 235, 0.35);
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
  --ease-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 1, 1);
  
  /* Layout */
  --max-width: 1400px;
  --content-max-width: 1200px;
  --sidebar-width: 260px;
  --min-card-width: 300px;
  
  /* Z-index Scale */
  --z-dropdown: 100;
  --z-sticky: 200;
  --z-overlay: 300;
  --z-modal: 400;
  --z-tooltip: 500;
  
  /* Borders */
  --border-width: 1px;
  --border-width-thick: 2px;
  --border-color: var(--color-gray-200);
  --border-color-light: var(--color-gray-100);
  
  /* Component Specific */
  --input-border: var(--border-width-thick) solid var(--color-gray-200);
  --input-padding-y: 14px;
  --input-padding-x: 20px;
  --button-padding-y: 12px;
  --button-padding-x: 24px;
  --card-padding: 32px;
  --card-padding-mobile: 24px;
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  :root {
    /* Dark mode tokens can be added here */
  }
}