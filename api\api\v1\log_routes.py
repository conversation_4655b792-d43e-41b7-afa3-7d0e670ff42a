"""
Log API routes for the TargetWise application.
Provides endpoints for accessing and managing application logs.
"""
import asyncio
import json
import os
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

from fastapi import APIRouter, Query, HTTPException
from fastapi.responses import JSONResponse

from api.utils.notifications import notify_info, notify_error

# Configure router
router = APIRouter()

# Default log path
DEFAULT_LOG_PATH = Path(__file__).parent.parent.parent.parent / "app.log"

# Log entry regex
LOG_PATTERN = re.compile(
    r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - ([^-]+) - ([A-Z]+) - (.*)"
)

@router.get("/logs")
async def get_logs(
    limit: int = Query(100, description="Maximum number of log entries to return"),
    level: Optional[str] = Query(None, description="Filter by log level (INFO, ERROR, etc.)"),
    source: Optional[str] = Query(None, description="Filter by log source/module"),
    search: Optional[str] = Query(None, description="Search logs for specific text"),
    start_date: Optional[str] = Query(None, description="Filter logs after this date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="Filter logs before this date (YYYY-MM-DD)"),
):
    """
    Get application logs with filtering options.
    """
    try:
        log_entries = []
        
        # Check if log file exists
        if not DEFAULT_LOG_PATH.exists():
            notify_error(f"Log file not found at {DEFAULT_LOG_PATH}", context="logs")
            return {"logs": [], "count": 0, "message": "Log file not found"}
        
        # Read log file in reverse (newest first)
        with open(DEFAULT_LOG_PATH, "r") as f:
            lines = f.readlines()
            
        # Process lines in reverse order (newest first)
        for line in reversed(lines):
            # Skip empty lines
            if not line.strip():
                continue
                
            # Parse log entry
            match = LOG_PATTERN.match(line)
            if not match:
                continue
                
            timestamp_str, source_module, log_level, message = match.groups()
            
            # Apply filters
            if level and level.upper() != log_level:
                continue
                
            if source and source.lower() not in source_module.lower():
                continue
                
            if search and search.lower() not in line.lower():
                continue
                
            # Parse timestamp
            try:
                timestamp = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S,%f")
            except ValueError:
                continue
                
            # Filter by date range
            if start_date:
                start = datetime.strptime(start_date, "%Y-%m-%d")
                if timestamp < start:
                    continue
                    
            if end_date:
                end = datetime.strptime(end_date, "%Y-%m-%d")
                if timestamp > end + timedelta(days=1):  # Include the full end day
                    continue
            
            # Add entry to results
            log_entries.append({
                "timestamp": timestamp_str,
                "source": source_module.strip(),
                "level": log_level,
                "message": message.strip()
            })
            
            # Stop if we've reached the limit
            if len(log_entries) >= limit:
                break
        
        # Get unique log levels and sources for filters
        all_levels = set()
        all_sources = set()
        
        with open(DEFAULT_LOG_PATH, "r") as f:
            for i, line in enumerate(f):
                if i > 1000:  # Limit for performance
                    break
                match = LOG_PATTERN.match(line)
                if match:
                    _, source_module, log_level, _ = match.groups()
                    all_levels.add(log_level)
                    all_sources.add(source_module.strip())
        
        notify_info(f"Retrieved {len(log_entries)} log entries", context="logs")
        return {
            "logs": log_entries,
            "count": len(log_entries),
            "metadata": {
                "levels": sorted(list(all_levels)),
                "sources": sorted(list(all_sources)),
                "log_file": str(DEFAULT_LOG_PATH),
                "log_size": os.path.getsize(DEFAULT_LOG_PATH)
            }
        }
        
    except Exception as e:
        notify_error(f"Error retrieving logs: {str(e)}", context="logs")
        raise HTTPException(status_code=500, detail=f"Error retrieving logs: {str(e)}")


@router.delete("/logs/clear")
async def clear_logs():
    """Clear the application log file."""
    try:
        # Backup existing log
        backup_path = DEFAULT_LOG_PATH.with_suffix('.log.bak')
        if DEFAULT_LOG_PATH.exists():
            with open(DEFAULT_LOG_PATH, 'rb') as src, open(backup_path, 'wb') as dst:
                dst.write(src.read())
            
            # Clear the log file
            with open(DEFAULT_LOG_PATH, 'w') as f:
                f.write(f"Log cleared at {datetime.now().isoformat()}\n")
            
            notify_info(f"Log file cleared and backed up to {backup_path}", context="logs")
            return {"success": True, "message": f"Log cleared and backed up to {backup_path}"}
        else:
            notify_error("Log file not found", context="logs")
            return {"success": False, "message": "Log file not found"}
            
    except Exception as e:
        notify_error(f"Error clearing logs: {str(e)}", context="logs")
        raise HTTPException(status_code=500, detail=f"Error clearing logs: {str(e)}")
