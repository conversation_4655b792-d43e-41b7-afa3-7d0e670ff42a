<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="TargetWise - AI-powered Facebook ad targeting with Meta Marketing API integration">
    <meta name="keywords" content="facebook ads, targeting, meta api, advertising, interests">
    <title>TargetWise - Algorithmic Targeting Sheet Builder</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/src/styles/index.css" as="style">
    <link rel="preload" href="/src/app.js" as="script" type="module">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="/src/styles/index.css">
    <link rel="stylesheet" href="/src/styles/enhanced-pages.css">
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/public/images/logo-icon.svg">
    
    <!-- Meta tags for SEO -->
    <meta property="og:title" content="TargetWise - AI-Powered Facebook Ad Targeting">
    <meta property="og:description" content="Build powerful 12-column targeting sheets using Meta Marketing API for optimized Facebook ad campaigns">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://targetwise.app">
    
    <!-- Performance optimizations -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="theme-color" content="#2563eb">
    
    <!-- PWA manifest -->
    <link rel="manifest" href="/manifest.json">
</head>
<body>
    <!-- Main Application Container -->
    <div id="app">
        <!-- Loading State -->
        <div class="min-h-screen bg-gray-50 flex items-center justify-center">
            <div class="text-center">
                <div class="spinner mx-auto mb-4"></div>
                <p class="text-gray-600">Loading TargetWise...</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script type="module" src="/src/app.js"></script>
    
    <!-- Service Worker Registration -->
    <script>
        // Register service worker for caching (optional)
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
    
    <!-- Analytics placeholder -->
    <script>
        // Add your analytics code here
        // Example: Google Analytics, Mixpanel, etc.
    </script>
</body>
</html>