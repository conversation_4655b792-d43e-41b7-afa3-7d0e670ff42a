"""Interest search related Facebook API operations."""

import logging
from typing import List, Optional

import httpx

from api.schemas import InterestItem
from api.utils.async_utils import process_items_concurrently
from api.utils.facebook_utils import get_mock_interests, interest_from_api_response
from .facebook_base import FacebookBaseService

logger = logging.getLogger(__name__)


class InterestSearchService(FacebookBaseService):
    async def search_interests(
        self, query: str, type: str = "adinterest", limit: int = 1000
    ) -> List[InterestItem]:
        """Search for interests using the `targetingsearch` endpoint."""
        logger.info(
            "Searching for interests with query: %s, type: %s, limit: %d",
            query,
            type,
            limit,
        )

        if not self.access_token or not self.api_version:
            logger.error("Missing API credentials. Cannot perform search.")
            return []

        endpoint = f"{self.base_url}/act_{self.ad_account_id}/targetingsearch"
        params = {
            "locale": "en_US",
            "type": type,
            "q": query,
            "fields": "id,name,audience_size_lower_bound,audience_size_upper_bound,path,topic,description,disambiguation_category,type",
            "limit": limit,
            "access_token": self.access_token,
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(endpoint, params=params)
            if response.status_code == 200:
                data = response.json()
                interests = []
                for item in data.get("data", []):
                    interests.append(
                        InterestItem(
                            id=item.get("id", ""),
                            name=item.get("name", ""),
                            audience_size_lower_bound=item.get("audience_size_lower_bound", 0),
                            audience_size_upper_bound=item.get("audience_size_upper_bound", 0),
                            path=item.get("path", []),
                            topic=item.get("topic", ""),
                            description=item.get("description", ""),
                            disambiguation_category=item.get("disambiguation_category", ""),
                            type=item.get("type", "interests"),
                        )
                    )
                logger.info("Found %d interests for query: %s", len(interests), query)
                return interests
            error_text = response.text
            logger.error(
                "Error searching interests: %s - %s", response.status_code, error_text
            )
            return []
        except Exception as e:
            logger.error("Exception while searching interests: %s", str(e))
            logger.exception("Detailed exception information:")
            return []

    async def fetch_interests(
        self, interest_names: List[str], limit: int = 1000
    ) -> List[InterestItem]:
        """Fetch interest data for a list of interest names."""
        logger.info(
            "Fetching data for %d interests: %s",
            len(interest_names),
            interest_names,
        )
        logger.info(
            "Using API version: %s, Ad Account ID: %s",
            self.api_version,
            self.ad_account_id,
        )

        async def fetch_single_interest(interest_name: str) -> List[InterestItem]:
            logger.info("Fetching data for interest: %s", interest_name)
            ad_account_id = (
                self.ad_account_id.replace("act_", "")
                if str(self.ad_account_id).startswith("act_")
                else self.ad_account_id
            )
            endpoint = f"https://graph.facebook.com/v22.0/act_{ad_account_id}/targetingsearch"

            from api.models.admin import AdminStore
            admin_store = AdminStore()
            credentials = admin_store.get_credentials()

            params = {
                "locale": "en_US",
                "type": "adinterest",
                "q": interest_name,
                "fields": "id,name,audience_size_lower_bound,audience_size_upper_bound,path,topic,description,type,popularity,is_trending,is_unavailable",
                "limit": limit,
                "access_token": credentials.access_token,
            }

            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(endpoint, params=params)
                if response.status_code == 200:
                    data = response.json()
                    interests = []
                    for item in data.get("data", []):
                        interests.append(
                            InterestItem(
                                id=item.get("id", ""),
                                name=item.get("name", ""),
                                audience_size_lower_bound=item.get("audience_size_lower_bound", 0),
                                audience_size_upper_bound=item.get("audience_size_upper_bound", 0),
                                path=item.get("path", []),
                                topic=item.get("topic", ""),
                                description=item.get("description", ""),
                                disambiguation_category=item.get("disambiguation_category", ""),
                                type=item.get("type", "interests"),
                            )
                        )
                    logger.info(
                        "Found %d interests for: %s", len(interests), interest_name
                    )
                    return interests
                else:
                    error_text = response.text
                    try:
                        error_json = response.json()
                        logger.error("Error response JSON: %s", error_json)
                    except Exception:
                        logger.error("Error response text: %s", error_text)
                    logger.error(
                        "Error fetching interests for %s: %s - %s",
                        interest_name,
                        response.status_code,
                        error_text,
                    )
                    return []
            except Exception as e:
                logger.error("Exception fetching interests for %s: %s", interest_name, str(e))
                logger.exception("Detailed exception information:")
                return []

        results = await process_items_concurrently(
            items=interest_names,
            process_func=fetch_single_interest,
            concurrency=5,
            flatten_results=True,
        )
        return results

    async def fetch_interests_by_ids(
        self, interest_ids: List[str], country_code: str
    ) -> List[InterestItem]:
        """Fetch interest data for a list of interest IDs."""
        logger.info("Fetching data for %d interest IDs", len(interest_ids))

        async def fetch_single_interest_by_id(interest_id: str) -> Optional[InterestItem]:
            cache_key = f"interest:{interest_id}:{country_code}"
            endpoint = f"{interest_id}"
            params = {
                "fields": "id,name,audience_size_lower_bound,audience_size_upper_bound,path,topic,description,type,popularity,is_trending,is_unavailable",
                "access_token": self.access_token,
            }
            success, result, error = await self.api_handler.make_api_request(
                endpoint=endpoint,
                params=params,
                cache_key=cache_key,
                cache_ttl=7 * 24 * 3600,
                score_cost=1,
                mock_data_func=lambda: get_mock_interests([f"Interest {interest_id}"])[0],
                response_processor=interest_from_api_response,
            )
            if success:
                logger.info("Found interest for ID: %s", interest_id)
                return result
            logger.error("Error fetching interest for ID %s: %s", interest_id, error)
            return None

        results = await process_items_concurrently(
            items=interest_ids, process_func=fetch_single_interest_by_id, concurrency=5
        )
        return [item for item in results if item is not None]
