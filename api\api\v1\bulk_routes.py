# // DOC: docs/micro-tools/search-bulk-feature.md
import asyncio
import logging
from datetime import datetime

from fastapi import APIRouter, HTTPException

from api.schemas import (
    BulkSearchRequest,
    BulkSearchResponse,
    BulkSuggestionsRequest,
    BulkSuggestionsResponse,
    DeduplicatedSuggestion,
    InterestItem,
    KeywordResult,
    SeedResult,
)
from api.services import InterestSearchService, SuggestionService
from api.utils.async_utils import gather_with_concurrency

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/targeting/bulk-search", response_model=BulkSearchResponse)
async def bulk_search_interests(req: BulkSearchRequest):
    """Bulk search for interests based on multiple keywords."""
    try:
        start_time = datetime.now()
        from api.models.admin import AdminStore

        admin_store = AdminStore()
        credentials = admin_store.get_credentials()

        fb_service = InterestSearchService(
            api_version=credentials.api_version,
            ad_account_id=credentials.ad_account_id,
            access_token=credentials.access_token,
        )

        results = []
        total_interests = 0
        concurrency = 5

        logger.info(
            f"Bulk search request: {req.keywords}, country: {req.country_code}, limit: {req.limit_per_keyword}"
        )


        async def fetch_with_backoff(keyword):
            retries = 0
            max_retries = 3
            while retries < max_retries:
                try:
                    logger.info(f"Fetching interests for keyword: {keyword}")
                    interests = await fb_service.fetch_interests(
                        [keyword], req.country_code, limit=req.limit_per_keyword
                    )
                    logger.info(
                        f"Found {len(interests)} interests for keyword: {keyword}"
                    )
                    return interests
                except Exception as e:
                    if "429" in str(e) or "rate limit" in str(e) or "error 17" in str(e):
                        backoff_time = min(2**retries, 32)
                        logger.warning(
                            f"Rate limit hit for keyword '{keyword}'. Backing off for {backoff_time}s (retry {retries+1}/{max_retries})"
                        )
                        await asyncio.sleep(backoff_time)
                        retries += 1
                    else:
                        logger.error(
                            f"Error fetching interests for keyword '{keyword}': {str(e)}"
                        )
                        if retries < max_retries - 1:
                            retries += 1
                            await asyncio.sleep(1)
                        else:
                            raise
            return []

        tasks = [fetch_with_backoff(keyword) for keyword in req.keywords]
        batch_results = await gather_with_concurrency(concurrency, *tasks)

        logger.info(f"Batch results: {[len(batch) for batch in batch_results]}")

        for keyword, interests in zip(req.keywords, batch_results):
            keyword_result = KeywordResult(keyword=keyword, interests=interests)
            results.append(keyword_result)
            total_interests += len(interests)

        processing_time = (datetime.now() - start_time).total_seconds() * 1000

        logger.info(
            f"Bulk search response: {len(results)} keywords, {total_interests} total interests, {processing_time:.2f}ms"
        )

        return BulkSearchResponse(
            results=results,
            total_interests_found=total_interests,
            processing_time_ms=int(processing_time),
        )
    except Exception as e:
        logger.error(f"Error performing bulk search: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error performing bulk search: {str(e)}")


@router.post("/targeting/bulk-suggestions", response_model=BulkSuggestionsResponse)
async def bulk_get_interest_suggestions(req: BulkSuggestionsRequest):
    """Get suggestions for interests based on multiple seeds."""
    try:
        start_time = datetime.now()
        from api.models.admin import AdminStore

        admin_store = AdminStore()
        credentials = admin_store.get_credentials()

        fb_service = SuggestionService(
            api_version=credentials.api_version,
            ad_account_id=credentials.ad_account_id,
            access_token=credentials.access_token,
        )

        results = []
        total_suggestions = 0
        concurrency = 5
        suggestion_map = {}

        logger.info(
            f"Bulk suggestions request: {req.interest_ids}, country: {req.country_code}, limit: {req.limit_per_seed}"
        )


        async def fetch_seed(seed_id):
            retries = 0
            max_retries = 3
            while retries < max_retries:
                try:
                    logger.info(f"Fetching seed interest: {seed_id}")
                    seed_interest = InterestItem(id=seed_id, name="")
                    seed_interests = await fb_service.fetch_interests_by_ids([seed_id], req.country_code)
                    if seed_interests:
                        seed_interest = seed_interests[0]
                        logger.info(f"Found seed interest: {seed_interest.name} ({seed_id})")
                    else:
                        logger.warning(f"Seed interest not found: {seed_id}")

                    logger.info(f"Fetching suggestions for seed: {seed_id}")
                    suggestions = await fb_service.fetch_suggestions(
                        [seed_interest], req.country_code, limit=req.limit_per_seed
                    )
                    logger.info(
                        f"Found {len(suggestions)} suggestions for seed: {seed_id}"
                    )

                    return (seed_id, seed_interest.name, suggestions)
                except Exception as e:
                    if "429" in str(e) or "rate limit" in str(e) or "error 17" in str(e):
                        backoff_time = min(2**retries, 32)
                        logger.warning(
                            f"Rate limit hit for seed ID '{seed_id}'. Backing off for {backoff_time}s (retry {retries+1}/{max_retries})"
                        )
                        await asyncio.sleep(backoff_time)
                        retries += 1
                    else:
                        logger.error(
                            f"Error fetching suggestions for seed ID '{seed_id}': {str(e)}"
                        )
                        if retries < max_retries - 1:
                            retries += 1
                            await asyncio.sleep(1)
                        else:
                            raise
            return (seed_id, "", [])

        tasks = [fetch_seed(interest_id) for interest_id in req.interest_ids]
        batch_results = await gather_with_concurrency(concurrency, *tasks)

        logger.info(
            f"Batch results: {[(seed_id, len(suggestions)) for seed_id, _, suggestions in batch_results]}"
        )

        for seed_id, seed_name, suggestions in batch_results:
            seed_result = SeedResult(
                seed_id=seed_id,
                seed_name=seed_name or f"Interest {seed_id}",
                suggestions=suggestions,
            )
            results.append(seed_result)
            total_suggestions += len(suggestions)
            if req.deduplicate:
                for suggestion in suggestions:
                    if suggestion.id in suggestion_map:
                        suggestion_map[suggestion.id][1].append(seed_id)
                    else:
                        suggestion_map[suggestion.id] = (suggestion, [seed_id])

        deduplicated_suggestions = []
        if req.deduplicate:
            for interest_id, (interest, seed_ids) in suggestion_map.items():
                if len(seed_ids) > 1:
                    deduplicated_suggestions.append(
                        DeduplicatedSuggestion(
                            interest=interest,
                            seed_ids=seed_ids,
                            relevance_score=len(seed_ids) / len(req.interest_ids),
                        )
                    )

        processing_time = (datetime.now() - start_time).total_seconds() * 1000

        logger.info(
            f"Bulk suggestions response: {len(results)} seed interests, {total_suggestions} total suggestions, {processing_time:.2f}ms"
        )

        return BulkSuggestionsResponse(
            results=results,
            deduplicated_suggestions=deduplicated_suggestions,
            total_suggestions_found=total_suggestions,
            unique_suggestions_found=len(suggestion_map),
            processing_time_ms=int(processing_time),
        )
    except Exception as e:
        logger.error(f"Error performing bulk suggestions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error performing bulk suggestions: {str(e)}")

