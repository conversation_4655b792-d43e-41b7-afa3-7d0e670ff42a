# // DOC: docs/micro-tools/search-bulk-feature.md
"""
Utility functions for error handling.
"""

import logging
import traceback
from typing import Any, Dict, Generic, Optional, Tuple, TypeVar, Union

from fastapi import HTTPException

logger = logging.getLogger(__name__)

T = TypeVar("T")


class Result(Generic[T]):
    """
    Result type for error handling.
    """

    def __init__(
        self,
        success: bool,
        value: Optional[T] = None,
        error: Optional[Union[str, Dict[str, Any]]] = None,
        status_code: int = 500,
    ):
        """
        Initialize the result.

        Args:
            success: Whether the operation was successful
            value: The result value if successful
            error: The error message or data if unsuccessful
            status_code: The HTTP status code if unsuccessful
        """
        self.success = success
        self.value = value
        self.error = error
        self.status_code = status_code

    @classmethod
    def ok(cls, value: T) -> "Result[T]":
        """
        Create a successful result.

        Args:
            value: The result value

        Returns:
            A successful result
        """
        return cls(success=True, value=value)

    @classmethod
    def fail(
        cls, error: Union[str, Dict[str, Any]], status_code: int = 500
    ) -> "Result[T]":
        """
        Create a failed result.

        Args:
            error: The error message or data
            status_code: The HTTP status code

        Returns:
            A failed result
        """
        return cls(success=False, error=error, status_code=status_code)

    def unwrap(self) -> T:
        """
        Unwrap the result value.

        Returns:
            The result value

        Raises:
            HTTPException: If the result is unsuccessful
        """
        if not self.success:
            error_message = str(self.error) if self.error else "Unknown error"
            raise HTTPException(status_code=self.status_code, detail=error_message)

        return self.value

    def unwrap_or(self, default: T) -> T:
        """
        Unwrap the result value or return a default value.

        Args:
            default: The default value

        Returns:
            The result value or the default value
        """
        if not self.success:
            return default

        return self.value

    def map(self, func: callable) -> "Result[Any]":
        """
        Map the result value.

        Args:
            func: Function to apply to the value

        Returns:
            A new result with the mapped value
        """
        if not self.success:
            return self

        try:
            return Result.ok(func(self.value))
        except Exception as e:
            logger.error(f"Error mapping result: {e}")
            return Result.fail(str(e))

    def __bool__(self) -> bool:
        """
        Convert the result to a boolean.

        Returns:
            True if successful, False otherwise
        """
        return self.success


def handle_exceptions(func):
    """
    Decorator to handle exceptions and return a Result.

    Args:
        func: Function to decorate

    Returns:
        Decorated function
    """

    async def wrapper(*args, **kwargs):
        try:
            result = await func(*args, **kwargs)
            return Result.ok(result)
        except HTTPException as e:
            logger.error(f"HTTP exception in {func.__name__}: {e.detail}")
            return Result.fail(e.detail, e.status_code)
        except Exception as e:
            logger.error(f"Exception in {func.__name__}: {str(e)}")
            logger.error(traceback.format_exc())
            return Result.fail(str(e))

    return wrapper


def format_error_response(
    message: str, code: int = 500, details: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Format an error response.

    Args:
        message: Error message
        code: Error code
        details: Additional error details

    Returns:
        Formatted error response
    """
    response = {"success": False, "error": {"message": message, "code": code}}

    if details:
        response["error"]["details"] = details

    return response


def parse_api_error(response_data: Dict[str, Any]) -> Tuple[str, int]:
    """
    Parse an API error response.

    Args:
        response_data: API response data

    Returns:
        Tuple of (error message, error code)
    """
    if "error" in response_data:
        error = response_data["error"]
        message = error.get("message", "Unknown error")
        code = error.get("code", 500)
        return message, code

    return "Unknown error", 500
