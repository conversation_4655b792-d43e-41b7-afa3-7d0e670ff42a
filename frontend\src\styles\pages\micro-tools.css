/*
 * Micro-tools specific styles
 * Works with the TargetWise Design System
 */

/* Micro-tools specific layout adjustments */
.micro-tools-sidebar {
  width: var(--sidebar-width);
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  height: fit-content;
  position: sticky;
  top: calc(var(--spacing-xl) + 80px); /* Account for header */
  flex-shrink: 0;
}

@media (max-width: 1024px) {
  .micro-tools-sidebar {
    display: none;
  }
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-base);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--color-gray-500);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

.sidebar-item:hover {
  background: var(--color-gray-50);
  color: var(--color-gray-800);
  transform: translateX(4px);
  text-decoration: none;
}

.sidebar-item.active {
  background: var(--color-primary-light);
  color: var(--color-primary);
}

.sidebar-icon {
  width: 20px;
  height: 20px;
  opacity: 0.7;
}

/* Page specific components */
.search-container {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-xl);
}

.search-header {
  margin-bottom: var(--spacing-xl);
}

.search-title {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-sm);
}

.search-subtitle {
  color: var(--color-gray-600);
  font-size: var(--font-size-body);
}

/* Search input group */
.search-input-group {
  position: relative;
  margin-bottom: var(--spacing-xl);
}

.search-input {
  width: 100%;
  padding: var(--spacing-base) var(--spacing-xl);
  padding-right: 120px;
  font-size: var(--font-size-body);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.search-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

/* Results grid */
.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.result-card {
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  transition: all var(--transition-normal);
  cursor: pointer;
}

.result-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.result-card.selected {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
}

/* Stats cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.stat-card {
  background: var(--color-white);
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
  transition: all var(--transition-normal);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-card:nth-child(1) .stat-icon {
  background: var(--bg-gradient-blue);
  color: var(--color-primary);
}

.stat-card:nth-child(2) .stat-icon {
  background: var(--bg-gradient-green);
  color: var(--color-success);
}

.stat-card:nth-child(3) .stat-icon {
  background: var(--bg-gradient-yellow);
  color: var(--color-warning);
}

.stat-card:nth-child(4) .stat-icon {
  background: var(--bg-gradient-purple);
  color: var(--color-purple);
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: var(--font-size-small);
  color: var(--color-gray-500);
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-800);
}

/* Suggestions specific */
.seed-library {
  background: var(--color-gray-50);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.seed-item {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-base);
  background: var(--color-white);
  border-radius: var(--radius-round);
  font-size: var(--font-size-small);
  margin: var(--spacing-xs);
  border: 1px solid var(--color-gray-200);
}

.seed-item button {
  background: none;
  border: none;
  color: var(--color-gray-400);
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.seed-item button:hover {
  color: var(--color-danger);
}

/* Loading states */
.skeleton {
  background: linear-gradient(90deg, var(--color-gray-100) 25%, var(--color-gray-50) 50%, var(--color-gray-100) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-card {
  height: 200px;
  border-radius: var(--radius-xl);
}

/* Empty states */
.empty-state {
  text-align: center;
  padding: var(--spacing-3xl);
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-xl);
  opacity: 0.5;
}

.empty-state-title {
  font-size: var(--font-size-h3);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-sm);
}

.empty-state-description {
  color: var(--color-gray-500);
}

/* Charts */
.chart-container {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-xl);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.chart-title {
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-800);
}

/* Tables */
.data-table {
  width: 100%;
  background: var(--color-white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.data-table th {
  background: var(--color-gray-50);
  padding: var(--spacing-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  text-align: left;
  border-bottom: 1px solid var(--color-gray-200);
}

.data-table td {
  padding: var(--spacing-base);
  border-bottom: 1px solid var(--color-gray-100);
}

.data-table tr:hover {
  background: var(--color-gray-50);
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xl);
}

/* Mobile responsive */
@media (max-width: 640px) {
  .search-container {
    padding: var(--spacing-xl);
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    width: 100%;
  }
}

/* Override Bootstrap conflicts */
.form-control:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.btn:focus {
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

/* Ensure proper font inheritance */
.micro-tools-container,
.micro-tools-container * {
  font-family: var(--font-sans);
}