# // DOC: docs/micro-tools/search-bulk-feature.md
import logging
import os
import re
from typing import Any, Dict, List, Optional, Set, Tuple

import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

from api.config.settings import settings
from api.schemas import InterestItem

logger = logging.getLogger(__name__)


class InterestClassifier:
    """
    Service for classifying Facebook interests into the 12 columns of Algorithmic Targeting 2.0.
    Uses a hybrid approach of rule-based classification and NLP-based similarity.
    """

    def __init__(self):
        # Load the sentence transformer model for embeddings
        self.use_nlp = False
        try:
            self.model = SentenceTransformer(settings.NLP_MODEL_NAME)
            self.use_nlp = True
            logger.info(f"Loaded NLP model: {settings.NLP_MODEL_NAME}")
        except Exception as e:
            logger.warning(
                f"Failed to load NLP model: {str(e)}. Falling back to rule-based classification only."
            )

        # Initialize column prototypes for NLP similarity
        self.column_prototypes = self._initialize_column_prototypes()

        # Initialize known lists for rule-based classification
        self.known_magazines = self._initialize_known_magazines()
        self.known_websites = self._initialize_known_websites()
        self.known_brands = self._initialize_known_brands()
        self.known_passionate_terms = self._initialize_known_passionate_terms()
        self.known_tv_shows = self._initialize_known_tv_shows()
        self.known_core_terms = self._initialize_known_core_terms()
        self.known_related_niches = self._initialize_known_related_niches()
        self.known_buyers = self._initialize_known_buyers()
        self.known_affinity_terms = self._initialize_known_affinity_terms()
        self.known_stores = self._initialize_known_stores()

        # Track assigned interests to prevent duplicates
        self.assigned_interests: Set[str] = set()

    def classify_interests(
        self, interests: List[InterestItem]
    ) -> Dict[int, List[InterestItem]]:
        """
        Classify a list of interests into the 12 columns of Algorithmic Targeting 2.0.

        Args:
            interests: List of InterestItem objects to classify

        Returns:
            Dictionary mapping column IDs to lists of interests
        """
        logger.info(f"Classifying {len(interests)} interests into 12 columns")

        # Reset assigned interests
        self.assigned_interests = set()

        # Initialize result dictionary with empty lists for each column
        classified_interests: Dict[int, List[InterestItem]] = {
            column["id"]: [] for column in settings.TARGETING_COLUMNS
        }

        # First pass: Rule-based classification for high-confidence assignments
        for interest in interests:
            column_id = self._classify_by_rules(interest)
            if column_id:
                classified_interests[column_id].append(interest)
                self.assigned_interests.add(interest.id)

        # Second pass: NLP-based classification for remaining interests
        if self.use_nlp:
            unassigned_interests = [
                i for i in interests if i.id not in self.assigned_interests
            ]
            if unassigned_interests:
                logger.info(
                    f"Using NLP to classify {len(unassigned_interests)} remaining interests"
                )
                for interest in unassigned_interests:
                    column_id = self._classify_by_nlp(interest)
                    if column_id:
                        classified_interests[column_id].append(interest)
                        self.assigned_interests.add(interest.id)

        # Third pass: Size-based classification for any remaining interests
        unassigned_interests = [
            i for i in interests if i.id not in self.assigned_interests
        ]
        if unassigned_interests:
            logger.info(
                f"Using size-based rules for {len(unassigned_interests)} remaining interests"
            )
            for interest in unassigned_interests:
                column_id = self._classify_by_size(interest)
                if column_id:
                    classified_interests[column_id].append(interest)
                    self.assigned_interests.add(interest.id)

        # Log classification results
        for column in settings.TARGETING_COLUMNS:
            column_id = column["id"]
            logger.info(
                f"Column {column_id} ({column['name']}): {len(classified_interests[column_id])} interests"
            )

        return classified_interests

    def _classify_by_rules(self, interest: InterestItem) -> Optional[int]:
        """
        Classify an interest using rule-based methods.

        Args:
            interest: The interest to classify

        Returns:
            Column ID if classified, None otherwise
        """
        # Extract useful properties
        name = interest.name.lower()
        topic = interest.topic.lower() if interest.topic else ""
        path = [p.lower() for p in interest.path] if interest.path else []
        description = interest.description.lower() if interest.description else ""

        # Get audience size (use upper bound if available, otherwise use a default)
        audience_size = interest.audience_size_upper_bound or 0

        # Column 3: Magazine Flex
        if (
            any(magazine.lower() in name for magazine in self.known_magazines)
            or "magazine" in name
            or "journal" in name
            or "publication" in name
            or ("news and entertainment" in topic and "magazine" in description)
        ):
            return 3

        # Column 4: Websites & Keywords
        if (
            any(website.lower() in name for website in self.known_websites)
            or ".com" in name
            or "blog" in name
            or "website" in name
            or "digital" in name
        ):
            return 4

        # Column 5: Niche Categories (Brands)
        if (
            any(brand.lower() in name for brand in self.known_brands)
            or "business and industry" in topic
            or "company" in description
            or "brand" in description
        ):
            return 5

        # Column 6: Passionate Flex
        if (
            any(term.lower() in name for term in self.known_passionate_terms)
            or "enthusiast" in name
            or "fan" in name
            or "lover" in name
            or "group" in name
            or "community" in name
            or "forum" in name
            or "club" in name
            or "hobbyist" in name
            or "i love" in name
        ):
            return 6

        # Column 7: TV Shows & Groups (Influencers)
        if (
            any(show.lower() in name for show in self.known_tv_shows)
            or "tv show" in name
            or "television" in name
            or "series" in name
            or "movie" in name
            or "film" in name
            or "actor" in name
            or "actress" in name
            or "celebrity" in name
            or "influencer" in name
            or "youtuber" in name
            or "public figure" in description
            or "entertainment" in topic
        ):
            return 7

        # Column 8: Core "Neck" Flex
        if any(term.lower() in name for term in self.known_core_terms):
            return 8

        # Column 9: Related Niche
        if any(niche.lower() in name for niche in self.known_related_niches):
            return 9

        # Column 10: Buyers Flex
        if (
            any(buyer.lower() in name for buyer in self.known_buyers)
            or "shopping" in topic
            or "e-commerce" in name
            or "online store" in description
            or "marketplace" in name
        ):
            return 10

        # Column 12: Store "Light" Flex (smaller retailers)
        if any(store.lower() in name for store in self.known_stores):
            return 12

        # Column 11: Affinity Flex
        if any(term.lower() in name for term in self.known_affinity_terms):
            return 11

        # No clear rule match
        return None

    def _classify_by_nlp(self, interest: InterestItem) -> Optional[int]:
        """
        Classify an interest using NLP-based similarity.

        Args:
            interest: The interest to classify

        Returns:
            Column ID if classified, None otherwise
        """
        if not self.use_nlp:
            return None

        # Create a text representation of the interest
        interest_text = f"{interest.name}"
        if interest.topic:
            interest_text += f" {interest.topic}"
        if interest.description:
            interest_text += f" {interest.description}"

        # Get the embedding for the interest
        interest_embedding = self.model.encode([interest_text])[0].reshape(1, -1)

        # Calculate similarity to each column prototype
        similarities = {}
        for column_id, prototype_embedding in self.column_prototypes.items():
            similarity = cosine_similarity(interest_embedding, prototype_embedding)[0][
                0
            ]
            similarities[column_id] = similarity

        # Find the column with the highest similarity
        best_column_id = max(similarities, key=similarities.get)
        best_similarity = similarities[best_column_id]

        # Only classify if similarity is above threshold
        if best_similarity > 0.3:  # Threshold can be adjusted
            return best_column_id

        return None

    def _classify_by_size(self, interest: InterestItem) -> int:
        """
        Classify an interest based on its audience size.
        This is a fallback method for interests that couldn't be classified by rules or NLP.

        Args:
            interest: The interest to classify

        Returns:
            Column ID (always returns a valid column)
        """
        # Get audience size (use upper bound if available)
        audience_size = interest.audience_size_upper_bound or 0

        # Column 1: Keyword Mix < 1M
        if audience_size < 1000000:
            return 1

        # Column 2: Keyword Mix > 1M
        return 2

    def _initialize_column_prototypes(self) -> Dict[int, np.ndarray]:
        """
        Initialize column prototypes for NLP similarity.

        Returns:
            Dictionary mapping column IDs to prototype embeddings
        """
        if not self.use_nlp:
            return {}

        # Define prototype texts for each column
        prototype_texts = {
            1: "small niche keywords interest under 1 million audience size",
            2: "large keywords interest over 1 million audience size",
            3: "magazine publication journal media press",
            4: "website blog digital online tech keywords",
            5: "brand company manufacturer product niche categories",
            6: "enthusiast fan community group forum passionate hobbyist",
            7: "tv show television series movie film actor actress celebrity influencer youtuber",
            8: "core main primary central essential fundamental key principal",
            9: "related adjacent similar associated connected linked comparable",
            10: "buyer shopper customer consumer purchase e-commerce marketplace",
            11: "affinity preference liking fondness inclination attraction",
            12: "store shop retailer outlet dealer vendor merchant",
        }

        # Encode prototype texts
        prototypes = {}
        for column_id, text in prototype_texts.items():
            embedding = self.model.encode([text])[0].reshape(1, -1)
            prototypes[column_id] = embedding

        return prototypes

    # Initialize known lists for rule-based classification
    # These would be populated with domain-specific terms in a real implementation

    def _initialize_known_magazines(self) -> List[str]:
        """Initialize list of known magazines/publications"""
        return [
            "Wired",
            "TechCrunch",
            "CNET Magazine",
            "PC Magazine",
            "What Hi-Fi",
            "Sound & Vision",
            "Home Theater Review",
            "Digital Trends",
            "T3",
            "Stuff",
            "Tech Radar",
            "AV Forums",
            "Home Cinema Choice",
        ]

    def _initialize_known_websites(self) -> List[str]:
        """Initialize list of known websites/blogs"""
        return [
            "CNET",
            "Engadget",
            "The Verge",
            "Gizmodo",
            "Lifehacker",
            "Mashable",
            "TechRadar",
            "AVSForum",
            "ProjectorCentral",
            "HomeTheaterReview.com",
            "DigitalTrends.com",
        ]

    def _initialize_known_brands(self) -> List[str]:
        """Initialize list of known brands in the niche"""
        return [
            "Epson",
            "BenQ",
            "Optoma",
            "Sony",
            "LG",
            "Samsung",
            "Anker",
            "JVC",
            "ViewSonic",
            "Panasonic",
            "Philips",
            "XGIMI",
            "Nebula",
            "Vava",
            "Hisense",
            "Magcubic",
            "Denon",
            "Marantz",
            "Onkyo",
        ]

    def _initialize_known_passionate_terms(self) -> List[str]:
        """Initialize list of known passionate community terms"""
        return [
            "Enthusiasts",
            "Fans",
            "Lovers",
            "Group",
            "Community",
            "Forum",
            "Club",
            "Hobbyist",
            "I love",
            "Passionate about",
            "Audiophile",
            "Cinephile",
            "Home Theater Enthusiasts",
        ]

    def _initialize_known_tv_shows(self) -> List[str]:
        """Initialize list of known TV shows, movies, influencers"""
        return [
            "Marques Brownlee",
            "MKBHD",
            "Unbox Therapy",
            "Linus Tech Tips",
            "AVS Forum",
            "Home Theater Geeks",
            "Netflix",
            "Disney+",
            "HBO Max",
            "Apple TV+",
            "Amazon Prime Video",
            "Hulu",
            "YouTube",
        ]

    def _initialize_known_core_terms(self) -> List[str]:
        """Initialize list of known core terms for the niche"""
        return [
            "Home theater",
            "Home cinema",
            "Projector",
            "4K",
            "Ultra HD",
            "Home entertainment",
            "Surround sound",
            "Home audio",
            "AV receiver",
        ]

    def _initialize_known_related_niches(self) -> List[str]:
        """Initialize list of known related niches"""
        return [
            "Video Games",
            "Gaming",
            "Photography",
            "Streaming",
            "Smart home",
            "Bluetooth speakers",
            "Headphones",
            "Virtual reality",
            "Augmented reality",
            "Smart TV",
            "HDTV",
            "Blu-ray",
        ]

    def _initialize_known_buyers(self) -> List[str]:
        """Initialize list of known buyer/marketplace terms"""
        return [
            "Amazon",
            "Best Buy",
            "Walmart",
            "Target",
            "Costco",
            "eBay",
            "Indiegogo",
            "Kickstarter",
            "B&H Photo Video",
            "Newegg",
            "Adorama",
            "Micro Center",
        ]

    def _initialize_known_affinity_terms(self) -> List[str]:
        """Initialize list of known affinity terms"""
        return [
            "Netflix",
            "Streaming",
            "Movies",
            "TV Shows",
            "Photography",
            "Gadget",
            "Technology",
            "Electronics",
            "Smart home",
            "Gaming",
            "Entertainment",
            "Bluetooth",
            "Wireless",
        ]

    def _initialize_known_stores(self) -> List[str]:
        """Initialize list of known specialty stores"""
        return [
            "Crutchfield",
            "Magnolia",
            "Fry's Electronics",
            "Micro Center",
            "Abt Electronics",
            "World Wide Stereo",
            "Audio Advice",
            "Value Electronics",
            "Projector People",
            "Projector Screen",
        ]
