# // DOC: docs/micro-tools/search-bulk-feature.md
"""
Utility functions for monitoring API calls and cache performance.
"""

import json
import logging
import os
import threading
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


class APIMonitor:
    """
    Monitor API calls and cache performance.
    """

    def __init__(self, log_file: Optional[str] = None):
        """
        Initialize the API monitor.

        Args:
            log_file: Path to the log file (default: None, logs to memory only)
        """
        self.log_file = log_file
        self.api_calls = 0
        self.api_successes = 0
        self.api_failures = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.response_times = []
        self.error_counts = {}
        self.endpoint_counts = {}
        self.start_time = time.time()
        self.lock = threading.Lock()

    def record_api_call(
        self,
        endpoint: str,
        success: bool,
        response_time: float,
        cache_hit: bool,
        error: Optional[Dict[str, Any]] = None,
    ):
        """
        Record an API call.

        Args:
            endpoint: API endpoint
            success: Whether the call was successful
            response_time: Response time in seconds
            cache_hit: Whether the call was served from cache
            error: Error details if the call failed
        """
        with self.lock:
            self.api_calls += 1

            if success:
                self.api_successes += 1
            else:
                self.api_failures += 1

                # Record error details
                if error:
                    error_message = error.get("error", {}).get(
                        "message", "Unknown error"
                    )
                    error_code = error.get("error", {}).get("code", 0)
                    error_key = f"{error_code}:{error_message[:50]}"

                    if error_key in self.error_counts:
                        self.error_counts[error_key] += 1
                    else:
                        self.error_counts[error_key] = 1

            # Record cache hit/miss
            if cache_hit:
                self.cache_hits += 1
            else:
                self.cache_misses += 1

            # Record response time
            self.response_times.append(response_time)

            # Record endpoint count
            if endpoint in self.endpoint_counts:
                self.endpoint_counts[endpoint] += 1
            else:
                self.endpoint_counts[endpoint] = 1

            # Log to file if specified
            if self.log_file:
                self._log_to_file(endpoint, success, response_time, cache_hit, error)

    def _log_to_file(
        self,
        endpoint: str,
        success: bool,
        response_time: float,
        cache_hit: bool,
        error: Optional[Dict[str, Any]],
    ):
        """
        Log an API call to a file.

        Args:
            endpoint: API endpoint
            success: Whether the call was successful
            response_time: Response time in seconds
            cache_hit: Whether the call was served from cache
            error: Error details if the call failed
        """
        try:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "endpoint": endpoint,
                "success": success,
                "response_time": response_time,
                "cache_hit": cache_hit,
            }

            if error:
                log_entry["error"] = error

            with open(self.log_file, "a") as f:
                f.write(json.dumps(log_entry) + "\n")
        except Exception as e:
            logger.error(f"Error logging to file: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about API calls and cache performance.

        Returns:
            Dictionary of statistics
        """
        with self.lock:
            uptime = time.time() - self.start_time

            # Calculate success rate
            success_rate = 0
            if self.api_calls > 0:
                success_rate = (self.api_successes / self.api_calls) * 100

            # Calculate cache hit rate
            cache_hit_rate = 0
            if self.api_calls > 0:
                cache_hit_rate = (self.cache_hits / self.api_calls) * 100

            # Calculate average response time
            avg_response_time = 0
            if self.response_times:
                avg_response_time = sum(self.response_times) / len(self.response_times)

            # Get top errors
            top_errors = sorted(
                self.error_counts.items(), key=lambda x: x[1], reverse=True
            )[:5]

            # Get top endpoints
            top_endpoints = sorted(
                self.endpoint_counts.items(), key=lambda x: x[1], reverse=True
            )[:5]

            return {
                "uptime_seconds": uptime,
                "uptime_formatted": str(timedelta(seconds=int(uptime))),
                "api_calls": self.api_calls,
                "api_successes": self.api_successes,
                "api_failures": self.api_failures,
                "success_rate": success_rate,
                "cache_hits": self.cache_hits,
                "cache_misses": self.cache_misses,
                "cache_hit_rate": cache_hit_rate,
                "avg_response_time": avg_response_time,
                "top_errors": top_errors,
                "top_endpoints": top_endpoints,
            }

    def reset_stats(self):
        """
        Reset all statistics.
        """
        with self.lock:
            self.api_calls = 0
            self.api_successes = 0
            self.api_failures = 0
            self.cache_hits = 0
            self.cache_misses = 0
            self.response_times = []
            self.error_counts = {}
            self.endpoint_counts = {}
            self.start_time = time.time()


# Global instance of the API monitor
api_monitor = APIMonitor(log_file=os.getenv("API_MONITOR_LOG_FILE"))
