# // DOC: docs/micro-tools/search-bulk-feature.md
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class TargetingStyle(str, Enum):
    """Targeting style enum for the 3 styles in Algorithmic Targeting 2.0"""

    OR_ONLY = "OR-Only"
    NARROWED = "Narrowed"
    SINGLE_INTEREST = "Single-Interest"


class SampleCSVType(str, Enum):
    """Types of sample CSV files"""

    SEARCH = "search"
    SUGGESTIONS = "suggestions"


class InterestItem(BaseModel):
    """Model for a Facebook interest"""

    id: str
    name: str
    audience_size_lower_bound: Optional[int] = None
    audience_size_upper_bound: Optional[int] = None
    audience_size: Optional[int] = None  # Calculated average for compatibility
    path: Optional[List[str]] = None
    topic: Optional[str] = None
    description: Optional[str] = None
    disambiguation_category: Optional[str] = None
    type: Optional[str] = None
    popularity: Optional[float] = None
    is_trending: Optional[bool] = None
    is_unavailable: Optional[bool] = None

    class Config:
        json_schema_extra = {
            "example": {
                "id": "6003135711081",
                "name": "Home theater",
                "audience_size_lower_bound": 20000000,
                "audience_size_upper_bound": 25000000,
                "audience_size": 22500000,
                "path": ["Interests", "Technology", "Home theater"],
                "topic": "Technology",
                "description": "Home entertainment system that reproduces a movie theater experience",
                "disambiguation_category": None,
                "type": "interests",
                "popularity": 0.8,
                "is_trending": False,
                "is_unavailable": False,
            }
        }


class InterestSuggestion(BaseModel):
    """Model for a suggested interest from Facebook API"""

    interest: InterestItem
    source_interest_id: str
    relevance_score: Optional[float] = None


class ColumnAssignment(BaseModel):
    """Model for an interest assigned to a specific column"""

    column_id: int
    column_name: str
    interests: List[InterestItem]
    narrow_interest: Optional[InterestItem] = None
    targeting_style: TargetingStyle
    estimated_reach_lower: Optional[int] = None
    estimated_reach_upper: Optional[int] = None
    needs_review: bool = False
    review_reason: Optional[str] = None
    rationale: Optional[str] = (
        None  # Explanation for why this column is grouped as such
    )
    manual_override: bool = (
        False  # True if user manually accepted column outside normal spec
    )


class TargetingSheet(BaseModel):
    """Model for the complete targeting sheet"""

    columns: List[ColumnAssignment]
    created_at: datetime = Field(default_factory=datetime.now)
    country_code: str
    age_min: int
    age_max: int
    seed_interests: List[str]
    total_interests_found: int
    total_suggestions_found: int


class TargetingRequest(BaseModel):
    """Model for a targeting sheet request"""

    seed_interests: List[str]
    country_code: str = "US"
    age_min: int = 18
    age_max: int = 65


class TargetingResponse(BaseModel):
    """Model for a targeting sheet response"""

    job_id: str
    status: str
    message: str
    output_file: Optional[str] = None
    error: Optional[str] = None


class ReachEstimateRequest(BaseModel):
    """Model for reach estimation request"""

    seed_interests: List[str]
    narrow_interest: Optional[str] = None
    country_code: str = "US"
    age_min: int = 18
    age_max: int = 65


class FacebookAPIError(BaseModel):
    """Model for Facebook API errors"""

    code: int
    message: str
    type: str
    fbtrace_id: Optional[str] = None


class ReachEstimateResponse(BaseModel):
    """Model for Facebook reach estimate response"""

    users_lower_bound: Optional[int] = None
    users_upper_bound: Optional[int] = None
    estimate_ready: bool = False
    estimate_dau: Optional[int] = None
    estimate_mau: Optional[int] = None
    error: Optional[FacebookAPIError] = None


class TargetingValidationResponse(BaseModel):
    """Model for Facebook targeting validation response"""

    valid: bool
    interests: List[InterestItem]
    error: Optional[FacebookAPIError] = None


class BulkSearchRequest(BaseModel):
    """Model for bulk interest search request"""

    keywords: List[str]
    country_code: str = "US"
    limit_per_keyword: int = 10


class KeywordResult(BaseModel):
    """Model for results of a single keyword search"""

    keyword: str
    interests: List[InterestItem]


class BulkSearchResponse(BaseModel):
    """Model for bulk interest search response"""

    results: List[KeywordResult]
    total_interests_found: int
    processing_time_ms: int


class BulkSuggestionsRequest(BaseModel):
    """Model for bulk interest suggestions request"""

    interest_ids: List[str]
    country_code: str = "US"
    limit_per_seed: int = 20
    deduplicate: bool = True


class SeedResult(BaseModel):
    """Model for results of a single seed interest"""

    seed_id: str
    seed_name: str
    suggestions: List[InterestItem]


class DeduplicatedSuggestion(BaseModel):
    """Model for a deduplicated suggestion that appeared for multiple seeds"""

    interest: InterestItem
    seed_ids: List[str]
    relevance_score: float


class BulkSuggestionsResponse(BaseModel):
    """Model for bulk interest suggestions response"""

    results: List[SeedResult]
    deduplicated_suggestions: List[DeduplicatedSuggestion]
    total_suggestions_found: int
    unique_suggestions_found: int
    processing_time_ms: int


class ExportCSVRequest(BaseModel):
    """Model for export to CSV request"""

    type: str  # "search_results" or "suggestion_results"
    data: List[Dict[str, Any]]
    format: str = "csv"  # "csv" or "xlsx"
