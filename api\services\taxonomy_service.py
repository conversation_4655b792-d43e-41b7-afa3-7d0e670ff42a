"""Interest taxonomy related Facebook API operations."""

import logging
from typing import Any, Dict, List, Optional

import httpx

from api.models.admin import AdminStore
from .facebook_base import FacebookBaseService

logger = logging.getLogger(__name__)


class TaxonomyService(FacebookBaseService):
    async def fetch_interest_taxonomy(
        self,
        parent_id: Optional[str] = None,
        limit_type: str = "interests",
        locale: str = "en_US",
    ) -> List[Dict[str, Any]]:
        """Fetch interest taxonomy using the `/targetingbrowse` endpoint."""

        ad_account_id = (
            self.ad_account_id.replace("act_", "")
            if str(self.ad_account_id).startswith("act_")
            else self.ad_account_id
        )
        endpoint = f"https://graph.facebook.com/v22.0/act_{ad_account_id}/targetingbrowse"

        admin_store = AdminStore()
        credentials = admin_store.get_credentials()

        params = {
            "access_token": credentials.access_token,
            "limit_type": limit_type,
            "locale": locale,
        }
        if parent_id:
            params["parent_id"] = parent_id

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(endpoint, params=params)
                if response.status_code == 200:
                    data = response.json()
                    result = data.get("data", [])
                    logger.info(
                        "Found %d taxonomy nodes for parent ID: %s",
                        len(result),
                        parent_id or "root",
                    )
                    return result
                logger.error(
                    "Error fetching taxonomy: %s - %s",
                    response.status_code,
                    response.text,
                )
                return []
        except Exception as e:
            logger.error("Exception fetching taxonomy: %s", str(e))
            return []

    async def browse_targeting_categories(
        self,
        category_type: str = "adtargetingcategory",
        targeting_class: str = "demographics",
    ) -> List[Dict[str, Any]]:
        """Browse targeting categories using the `/targetingbrowse` endpoint."""
        logger.info("Browsing targeting categories for class: %s", targeting_class)

        endpoint = f"https://graph.facebook.com/v22.0/act_{self.ad_account_id}/targetingbrowse"

        from api.models.admin import AdminStore

        admin_store = AdminStore()
        credentials = admin_store.get_credentials()

        params = {
            "type": category_type,
            "class": targeting_class,
            "access_token": credentials.access_token,
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(endpoint, params=params)
                if response.status_code == 200:
                    data = response.json()
                    result = data.get("data", [])
                    logger.info(
                        "Found %d targeting options for %s",
                        len(result),
                        targeting_class,
                    )
                    return result
                logger.error(
                    "Error browsing targeting categories: %s - %s",
                    response.status_code,
                    response.text,
                )
                return []
        except Exception as e:
            logger.error("Exception browsing targeting categories: %s", str(e))
            return []
