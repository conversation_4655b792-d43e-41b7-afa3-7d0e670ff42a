"""Base utilities for Facebook API services."""

import logging
import os
import time
from typing import List

from api.config.settings import settings
from api.schemas import InterestItem
from api.utils.api_utils import ApiRequestHandler
from api.utils.cache_utils import RedisCache, TieredCache
from api.utils.facebook_utils import get_mock_interests, get_mock_interests_by_ids, get_mock_suggestions

logger = logging.getLogger(__name__)


class FacebookBaseService:
    """Shared initialization and helpers for Facebook API services."""

    def __init__(self, api_version=None, ad_account_id=None, access_token=None):
        self.api_version = (
            api_version
            or os.getenv("FACEBOOK_API_VERSION")
            or settings.FACEBOOK_API_VERSION
        )
        self.ad_account_id = (
            ad_account_id
            or os.getenv("FACEBOOK_AD_ACCOUNT_ID")
            or settings.FACEBOOK_AD_ACCOUNT_ID
        )
        self.access_token = (
            access_token
            or os.getenv("FACEBOOK_ACCESS_TOKEN")
            or settings.FACEBOOK_ACCESS_TOKEN
        )

        self.base_url = f"https://graph.facebook.com/{self.api_version}"

        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        self.redis_cache = RedisCache(url=redis_url)
        self.tiered_cache = TieredCache(redis_cache=self.redis_cache)
        self.api_handler = ApiRequestHandler(
            base_url=self.base_url,
            access_token=self.access_token,
            redis_cache=self.redis_cache,
            rate_limit_score=0,
            api_rate_limit_score=int(os.getenv("API_RATE_LIMIT_SCORE", "60")),
            api_rate_limit_decay_seconds=int(
                os.getenv("API_RATE_LIMIT_DECAY_SECONDS", "300")
            ),
            last_call_time=time.time(),
        )

    async def get_redis(self):
        await self.redis_cache.connect()
        return self.redis_cache

    def _get_mock_interests(self, interest_names: List[str]) -> List[InterestItem]:
        """Return mock `InterestItem` objects for the given names."""
        return get_mock_interests(interest_names, testing_only=True)

    def _get_mock_interests_by_ids(self, interest_ids: List[str]) -> List[InterestItem]:
        """Return mock `InterestItem` objects for the given IDs."""
        return get_mock_interests_by_ids(interest_ids, testing_only=True)

    def _get_mock_suggestions(self, seed_interests: List[InterestItem]) -> List[InterestItem]:
        """Return mock suggestions for the given seed interests."""
        return get_mock_suggestions(seed_interests, testing_only=True)

    def get_reset_time(self) -> int:
        """Return seconds until the rate limit resets."""
        if self.api_handler.rate_limit_score <= 0:
            return 0
        now = time.time()
        elapsed = now - self.api_handler.last_call_time
        decay = self.api_handler.api_rate_limit_decay_seconds
        max_score = self.api_handler.api_rate_limit_score
        if elapsed >= decay:
            return 0
        if elapsed < 1:
            if self.api_handler.rate_limit_score < max_score:
                return int(decay * (self.api_handler.rate_limit_score / max_score))
            return decay
        return int(round(decay - elapsed))
