# // DOC: docs/micro-tools/search-bulk-feature.md
from fastapi import APIRouter

from .admin_routes import router as admin_router
from .search_routes import router as search_router
from .bulk_routes import router as bulk_router
from .utility_routes import router as utility_router
from .notification_routes import router as notification_router

api_router = APIRouter()
api_router.include_router(search_router)
api_router.include_router(bulk_router)
api_router.include_router(utility_router)
api_router.include_router(notification_router)

__all__ = ["api_router", "admin_router"]
