# // DOC: docs/micro-tools/search-bulk-feature.md
"""
Utility functions for caching.
"""

import hashlib
import json
import logging
from typing import Any, Dict, Generic, Optional, TypeVar, Union

logger = logging.getLogger(__name__)

T = TypeVar("T")


class RedisCache:
    """
    Redis cache wrapper for async operations.
    """

    def __init__(self, url: str, enabled: bool = True):
        """
        Initialize the Redis cache.

        Args:
            url: Redis URL
            enabled: Whether the cache is enabled
        """
        self.url = url
        self.enabled = enabled
        self.redis = None

    async def connect(self):
        """
        Connect to Redis.
        """
        if self.enabled and self.redis is None:
            try:
                import redis.asyncio as redis

                self.redis = await redis.from_url(
                    self.url, encoding="utf-8", decode_responses=True
                )
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {e}")
                self.enabled = False
                self.redis = None

    async def get(self, key: str) -> Optional[str]:
        """
        Get a value from the cache.

        Args:
            key: Cache key

        Returns:
            The cached value or None if not found
        """
        if not self.enabled:
            return None

        try:
            await self.connect()
            if self.redis:
                return await self.redis.get(key)
        except Exception as e:
            logger.error(f"Redis get error: {e}")

        return None

    async def set(self, key: str, value: str, ttl: int) -> bool:
        """
        Set a value in the cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds

        Returns:
            True if successful, False otherwise
        """
        if not self.enabled:
            return False

        try:
            await self.connect()
            if self.redis:
                await self.redis.set(key, value, ex=ttl)
                return True
        except Exception as e:
            logger.error(f"Redis set error: {e}")

        return False

    async def delete(self, key: str) -> bool:
        """
        Delete a value from the cache.

        Args:
            key: Cache key

        Returns:
            True if successful, False otherwise
        """
        if not self.enabled:
            return False

        try:
            await self.connect()
            if self.redis:
                await self.redis.delete(key)
                return True
        except Exception as e:
            logger.error(f"Redis delete error: {e}")

        return False

    async def exists(self, key: str) -> bool:
        """
        Check if a key exists in the cache.

        Args:
            key: Cache key

        Returns:
            True if the key exists, False otherwise
        """
        if not self.enabled:
            return False

        try:
            await self.connect()
            if self.redis:
                return await self.redis.exists(key) > 0
        except Exception as e:
            logger.error(f"Redis exists error: {e}")

        return False


class TieredCache(Generic[T]):
    """
    Tiered caching with memory and Redis.
    """

    def __init__(
        self,
        redis_cache: Optional[RedisCache] = None,
        memory_cache: Optional[Dict[str, Any]] = None,
    ):
        """
        Initialize the tiered cache.

        Args:
            redis_cache: Redis cache instance
            memory_cache: Dictionary for memory caching
        """
        self.redis_cache = redis_cache
        self.memory_cache = memory_cache or {}

    async def get(
        self, key: str, deserializer: Optional[callable] = None
    ) -> Optional[T]:
        """
        Get a value from the cache.

        Args:
            key: Cache key
            deserializer: Function to deserialize the cached value

        Returns:
            The cached value or None if not found
        """
        # Check memory cache first
        if key in self.memory_cache:
            logger.debug(f"Memory cache hit for key: {key}")
            return self.memory_cache[key]

        # Check Redis cache
        if self.redis_cache:
            cached = await self.redis_cache.get(key)
            if cached:
                logger.debug(f"Redis cache hit for key: {key}")
                value = json.loads(cached)
                if deserializer:
                    value = deserializer(value)
                self.memory_cache[key] = value
                return value

        return None

    async def set(
        self,
        key: str,
        value: T,
        ttl: int = 86400,
        serializer: Optional[callable] = None,
    ) -> bool:
        """
        Set a value in the cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds
            serializer: Function to serialize the value

        Returns:
            True if successful, False otherwise
        """
        # Set in memory cache
        self.memory_cache[key] = value

        # Set in Redis cache
        if self.redis_cache:
            try:
                if serializer:
                    serialized = serializer(value)
                elif hasattr(value, "model_dump_json"):
                    serialized = value.model_dump_json()
                elif hasattr(value, "__dict__"):
                    serialized = json.dumps(value.__dict__)
                else:
                    serialized = json.dumps(value)

                return await self.redis_cache.set(key, serialized, ttl)
            except Exception as e:
                logger.error(f"Cache set error: {e}")

        return False

    async def delete(self, key: str) -> bool:
        """
        Delete a value from the cache.

        Args:
            key: Cache key

        Returns:
            True if successful, False otherwise
        """
        # Delete from memory cache
        if key in self.memory_cache:
            del self.memory_cache[key]

        # Delete from Redis cache
        if self.redis_cache:
            return await self.redis_cache.delete(key)

        return True

    @staticmethod
    def create_key(*parts: Union[str, int]) -> str:
        """
        Create a cache key from parts.

        Args:
            parts: Parts to include in the key

        Returns:
            Cache key
        """
        return ":".join(str(part) for part in parts)

    @staticmethod
    def hash_key(key: str) -> str:
        """
        Create a hash of a key.

        Args:
            key: Key to hash

        Returns:
            Hashed key
        """
        return hashlib.sha1(key.encode()).hexdigest()
