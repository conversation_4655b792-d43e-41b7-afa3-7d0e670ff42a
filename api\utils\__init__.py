# // DOC: docs/micro-tools/search-bulk-feature.md
"""
Utility functions for the application.
"""

from api.utils.api_utils import ApiRequestHandler
from api.utils.async_utils import (
    gather_with_concurrency,
    process_items_concurrently,
    retry_with_backoff,
)
from api.utils.cache_utils import RedisCache, TieredCache
from api.utils.error_utils import (
    Result,
    format_error_response,
    handle_exceptions,
    parse_api_error,
)
from api.utils.facebook_utils import (
    create_targeting_spec,
    get_mock_interests,
    get_mock_interests_by_ids,
    get_mock_suggestions,
    get_mock_taxonomy,
    interest_from_api_response,
    interests_from_api_response,
)

__all__ = [
    "ApiRequestHandler",
    "gather_with_concurrency",
    "process_items_concurrently",
    "retry_with_backoff",
    "RedisCache",
    "TieredCache",
    "Result",
    "handle_exceptions",
    "format_error_response",
    "parse_api_error",
    "create_targeting_spec",
    "interest_from_api_response",
    "interests_from_api_response",
    "get_mock_interests",
    "get_mock_interests_by_ids",
    "get_mock_suggestions",
    "get_mock_taxonomy",
]
