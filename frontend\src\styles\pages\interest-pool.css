/* Interest Pool Page Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #f0f2f5;
    color: #1a1a2e;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: 700;
    color: #2563eb;
    text-decoration: none;
    transition: transform 0.2s;
}

.logo:hover {
    transform: translateY(-1px);
}

.logo svg {
    width: 32px;
    height: 32px;
}

.nav-menu {
    display: flex;
    gap: 8px;
    align-items: center;
}

.nav-link {
    padding: 8px 16px;
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s;
}

.nav-link:hover {
    color: #2563eb;
    background: rgba(37, 99, 235, 0.08);
}

.nav-link.active {
    color: #2563eb;
    background: rgba(37, 99, 235, 0.1);
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.theme-toggle {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: none;
    background: #f1f5f9;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.theme-toggle:hover {
    background: #e2e8f0;
}

.btn {
    padding: 10px 20px;
    border-radius: 8px;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-outline {
    background: transparent;
    color: #64748b;
    border: 2px solid #e2e8f0;
}

.btn-outline:hover {
    border-color: #cbd5e1;
    background: #f8fafc;
}

.btn-primary {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    color: white;
    box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.35);
}

/* Main Layout */
.main-container {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    gap: 24px;
    padding: 24px;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.sidebar-title {
    font-size: 14px;
    font-weight: 600;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 16px;
}

.sidebar-menu {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.sidebar-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 10px;
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    transition: all 0.2s;
}

.sidebar-item:hover {
    background: #f1f5f9;
    color: #1e293b;
    transform: translateX(4px);
}

.sidebar-item.active {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    color: #2563eb;
}

.sidebar-icon {
    width: 20px;
    height: 20px;
    opacity: 0.7;
}

/* Content Area */
.content {
    flex: 1;
}

/* Page Header */
.page-header {
    background: white;
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.page-header-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 16px;
}

.page-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.page-subtitle {
    color: #64748b;
    font-size: 16px;
    margin-left: 64px;
}

.page-actions {
    display: flex;
    gap: 12px;
}

.btn-secondary {
    background: #f1f5f9;
    color: #475569;
}

.btn-secondary:hover {
    background: #e2e8f0;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.stat-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

.stat-label {
    color: #64748b;
    font-size: 14px;
}

/* Collection Info */
.collection-info {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 2px solid #3b82f6;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
}

.info-icon {
    width: 40px;
    height: 40px;
    background: #2563eb;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.info-text {
    color: #1e40af;
    line-height: 1.6;
    flex: 1;
}

/* Actions Bar */
.actions-bar {
    background: white;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.action-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
    color: white;
}

.selection-info {
    display: flex;
    align-items: center;
    gap: 16px;
    color: #64748b;
}

/* Filters */
.filters-section {
    background: white;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.filters-title {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 12px;
}

.filters-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 2fr;
    gap: 16px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-label {
    font-size: 14px;
    font-weight: 500;
    color: #475569;
}

.filter-input {
    padding: 10px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s;
}

.filter-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.range-inputs {
    display: flex;
    gap: 8px;
    align-items: center;
}

.range-input {
    flex: 1;
}

.range-separator {
    color: #94a3b8;
}

.btn-clear-filters {
    background: white;
    color: #8b5cf6;
    border: 2px solid #8b5cf6;
}

.btn-clear-filters:hover {
    background: rgba(139, 92, 246, 0.05);
}

/* Table */
.table-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

.table-header {
    padding: 24px 24px 16px;
    border-bottom: 1px solid #f1f5f9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-title {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
}

.table-wrapper {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th {
    text-align: left;
    padding: 16px 24px;
    font-weight: 600;
    color: #475569;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: #fafbfc;
    border-bottom: 2px solid #f1f5f9;
    position: sticky;
    top: 0;
    z-index: 10;
}

td {
    padding: 16px 24px;
    border-bottom: 1px solid #f1f5f9;
}

tr:hover {
    background: #fafbfc;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
}

.checkbox {
    width: 20px;
    height: 20px;
    cursor: pointer;
    accent-color: #2563eb;
}

.interest-id {
    font-family: monospace;
    color: #64748b;
    font-size: 13px;
}

.interest-name {
    font-weight: 500;
    color: #1e293b;
}

.audience-badge {
    display: inline-block;
    padding: 4px 12px;
    background: #e0f2fe;
    color: #0369a1;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
}

.interest-type {
    display: inline-block;
    padding: 4px 12px;
    background: #f3e8ff;
    color: #7c3aed;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
}

.path-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #64748b;
    font-size: 13px;
}

.path-separator {
    color: #cbd5e1;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    background: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    color: #64748b;
}

.action-btn:hover {
    border-color: #2563eb;
    color: #2563eb;
    background: rgba(37, 99, 235, 0.05);
}

.action-btn.delete-btn:hover {
    border-color: #ef4444;
    color: #ef4444;
    background: rgba(239, 68, 68, 0.05);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 80px 20px;
}

.empty-icon {
    width: 100px;
    height: 100px;
    background: #f1f5f9;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 48px;
}

.empty-title {
    font-size: 24px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
}

.empty-text {
    color: #64748b;
    margin-bottom: 24px;
}

/* Responsive */
@media (max-width: 1024px) {
    .sidebar {
        display: none;
    }
    
    .main-container {
        padding: 16px;
    }
    
    .filters-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 640px) {
    .header-content {
        padding: 12px 16px;
    }
    
    .nav-menu {
        display: none;
    }
    
    .page-header {
        padding: 24px 16px;
    }
    
    .page-title {
        font-size: 24px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        width: 100%;
    }
    
    .btn {
        flex: 1;
        justify-content: center;
    }
}