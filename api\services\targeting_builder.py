# // DOC: docs/micro-tools/search-bulk-feature.md
import logging
import os
import random
from datetime import datetime
from typing import Any, Dict, List, Optional, Set

import pandas as pd

from api.config.settings import settings
from api.schemas import ColumnAssignment, InterestItem, TargetingSheet, TargetingStyle

logger = logging.getLogger(__name__)


class TargetingBuilder:
    """
    Service for building the 12-column targeting sheet based on classified interests.
    Handles targeting style assignment, deduplication, and output generation.
    """

    def __init__(self):
        # Track assigned interests to prevent duplicates
        self.assigned_interests: Set[str] = set()

    def build_targeting_sheet(
        self,
        classified_interests: Dict[int, List[InterestItem]],
        country_code: str,
        age_min: int,
        age_max: int,
    ) -> TargetingSheet:
        """
        Build a targeting sheet from classified interests.

        Args:
            classified_interests: Dictionary mapping column IDs to lists of interests
            country_code: Country code for targeting
            age_min: Minimum age for targeting
            age_max: Maximum age for targeting

        Returns:
            TargetingSheet object with all columns populated
        """
        logger.info("Building targeting sheet from classified interests")

        # Reset assigned interests
        self.assigned_interests = set()

        # Initialize columns list
        columns = []

        # Get the targeting style distribution (4-4-4 rule)
        style_assignments = self._assign_targeting_styles()

        # Rationale mapping for each column (strict 12-column mapping)
        rationale_map = {
            1: "Keyword Mix < 1M: Small, niche interests under 1 million audience size.",
            2: "Keyword Mix > 1M: Larger keyword interests over 1 million audience size.",
            3: "Magazine Flex: Interests in magazines, publications, and media relevant to the niche.",
            4: "Websites & Keywords: Interests in websites, blogs, and digital properties.",
            5: "Niche Categories: Interests in niche categories or specific product brands.",
            6: "Passionate Flex: Interests reflecting passionate or highly engaged audiences.",
            7: "TV Shows & Groups: TV shows, groups, influencers, and pop culture relevant to the niche.",
            8: "Core 'Neck' Flex: Core, main, or primary interests central to the product's niche.",
            9: "Related Niche: Interests in adjacent or related niches.",
            10: "Buyers Flex: Buyer, shopper, or e-commerce-related interests.",
            11: "Affinity Flex: Interests showing affinity, preference, or strong inclination.",
            12: "Store 'Light' Flex: Interests in specialty stores, retailers, or merchants.",
        }
        # Process each column
        for column_config in settings.TARGETING_COLUMNS:
            column_id = column_config["id"]
            column_name = column_config["name"]
            rationale = rationale_map.get(column_id, "")

            # Get the targeting style for this column
            targeting_style = style_assignments[column_id]

            # Get interests for this column
            interests = classified_interests.get(column_id, [])

            # Skip if no interests available
            if not interests:
                logger.warning(
                    f"No interests available for column {column_id} ({column_name})"
                )
                # Create an empty column with needs_review flag
                columns.append(
                    ColumnAssignment(
                        column_id=column_id,
                        column_name=column_name,
                        interests=[],
                        targeting_style=targeting_style,
                        needs_review=True,
                        review_reason="No interests available for this column",
                        rationale=rationale,
                    )
                )
                continue

            # Filter out already assigned interests
            available_interests = [
                i for i in interests if i.id not in self.assigned_interests
            ]

            if not available_interests:
                logger.warning(
                    f"All interests for column {column_id} ({column_name}) already assigned to other columns"
                )
                # Create an empty column with needs_review flag
                columns.append(
                    ColumnAssignment(
                        column_id=column_id,
                        column_name=column_name,
                        interests=[],
                        targeting_style=targeting_style,
                        needs_review=True,
                        review_reason="All interests already assigned to other columns",
                        rationale=rationale,
                    )
                )
                continue

            # Sort interests by audience size (descending)
            available_interests.sort(
                key=lambda i: i.audience_size_upper_bound or 0, reverse=True
            )

            # Build the column based on targeting style
            if targeting_style == TargetingStyle.SINGLE_INTEREST:
                # Style 3: Single-Interest Targeting
                # Choose the largest interest
                selected_interest = available_interests[0]
                column_interests = [selected_interest]
                narrow_interest = None

                # Mark as assigned
                self.assigned_interests.add(selected_interest.id)

            elif targeting_style == TargetingStyle.NARROWED:
                # Style 2: Narrowed Targeting
                # Choose up to 8 interests for the first layer
                first_layer_interests = available_interests[
                    : min(8, len(available_interests))
                ]

                # Choose a broad interest for narrowing
                # Ideally from a different column to avoid overlap
                narrow_interest = self._find_narrow_interest(
                    classified_interests, column_id, first_layer_interests
                )

                column_interests = first_layer_interests

                # Mark as assigned
                for interest in column_interests:
                    self.assigned_interests.add(interest.id)

                if narrow_interest:
                    self.assigned_interests.add(narrow_interest.id)

            else:
                # Style 1: OR-Only Targeting
                # Choose up to 8 interests
                column_interests = available_interests[
                    : min(8, len(available_interests))
                ]
                narrow_interest = None

                # Mark as assigned
                for interest in column_interests:
                    self.assigned_interests.add(interest.id)

            # Create the column assignment
            column = ColumnAssignment(
                column_id=column_id,
                column_name=column_name,
                interests=column_interests,
                narrow_interest=narrow_interest,
                targeting_style=targeting_style,
            )

            columns.append(column)

        # Create the targeting sheet
        targeting_sheet = TargetingSheet(
            columns=columns,
            created_at=datetime.now(),
            country_code=country_code,
            age_min=age_min,
            age_max=age_max,
            seed_interests=[],  # Will be populated by the caller
            total_interests_found=sum(
                len(interests) for interests in classified_interests.values()
            ),
            total_suggestions_found=0,  # Will be populated by the caller
        )

        return targeting_sheet

    def validate_quality_gates(self, targeting_sheet: TargetingSheet) -> Dict[str, Any]:
        """
        Validate the targeting sheet against quality gates before export.

        Args:
            targeting_sheet: The targeting sheet to validate

        Returns:
            Dictionary with validation results
        """
        logger.info("Validating targeting sheet against quality gates")
        result = {"passed": True, "messages": [], "blocking_issues": False}

        # Gate 1: Style Mix - Must have 4 columns of each style
        style_counts = {
            style: 0
            for style in [
                TargetingStyle.OR_ONLY,
                TargetingStyle.NARROWED,
                TargetingStyle.SINGLE_INTEREST,
            ]
        }
        for column in targeting_sheet.columns:
            if column.targeting_style in style_counts:
                style_counts[column.targeting_style] += 1

        style_correct = all(count == 4 for count in style_counts.values())
        if not style_correct:
            result["passed"] = False
            result["blocking_issues"] = True  # This is a blocking issue
            style_msg = (
                f"Style distribution is not 4-4-4: {style_counts}. Export is blocked."
            )
            result["messages"].append(style_msg)
            logger.warning(style_msg)

        # Gate 2: Duplicates - Interest ID must be unique across columns
        interest_ids = {}
        has_duplicates = False
        for i, column in enumerate(targeting_sheet.columns):
            for interest in column.interests:
                if interest.id in interest_ids:
                    has_duplicates = True
                    prev_col = interest_ids[interest.id]
                    result["messages"].append(
                        f"Duplicate interest: '{interest.name}' in columns {prev_col+1} and {i+1}. Auto-deduplication required."
                    )
                else:
                    interest_ids[interest.id] = i

            if column.narrow_interest and column.narrow_interest.id in interest_ids:
                has_duplicates = True
                prev_col = interest_ids[column.narrow_interest.id]
                result["messages"].append(
                    f"Duplicate interest: '{column.narrow_interest.name}' as narrow in column {i+1} and regular in column {prev_col+1}. Auto-deduplication required."
                )

        if has_duplicates:
            result["passed"] = False
            logger.warning("Duplicate interests found across columns")

        # Gate 3: Reach Window - Each column should be in 4-5M range
        reach_issues = 0
        for i, column in enumerate(targeting_sheet.columns):
            if (
                column.estimated_reach_lower is None
                or column.estimated_reach_upper is None
            ):
                result["messages"].append(f"Column {i+1} missing reach estimate")
                reach_issues += 1
                continue

            midpoint = (column.estimated_reach_lower + column.estimated_reach_upper) / 2
            if (
                midpoint < settings.TARGET_AUDIENCE_SIZE_MIN
                or midpoint > settings.TARGET_AUDIENCE_SIZE_MAX
            ):
                if not column.needs_review:
                    column.needs_review = True
                    column.review_reason = f"Audience size {midpoint:,} outside target range ({settings.TARGET_AUDIENCE_SIZE_MIN:,}-{settings.TARGET_AUDIENCE_SIZE_MAX:,})"

                result["messages"].append(
                    f"Column {i+1} audience size {midpoint:,} outside target range"
                )
                reach_issues += 1

        if reach_issues > 0:
            result["passed"] = False
            logger.warning(f"{reach_issues} columns have reach outside target range")

        if result["passed"]:
            logger.info("All quality gates passed")

        return result

    def generate_excel(self, targeting_sheet: TargetingSheet, output_path: str) -> str:
        """
        Generate an Excel file from the targeting sheet.

        Args:
            targeting_sheet: The targeting sheet to export
            output_path: Path to save the Excel file

        Returns:
            Path to the generated Excel file
        """
        logger.info(f"Generating Excel file at {output_path}")

        # Create a DataFrame for the main targeting sheet
        data = []

        for column in targeting_sheet.columns:
            # Format the interests based on targeting style
            if column.targeting_style == TargetingStyle.SINGLE_INTEREST:
                # Style 3: Single interest
                interests_text = (
                    column.interests[0].name if column.interests else "NEEDS REVIEW"
                )

            elif column.targeting_style == TargetingStyle.NARROWED:
                # Style 2: Narrowed targeting
                interests_text = " OR ".join([i.name for i in column.interests])
                if column.narrow_interest:
                    interests_text += f"\n\nNARROWED BY: {column.narrow_interest.name}"
                else:
                    interests_text += "\n\nNARROWED BY: NEEDS REVIEW"

            else:
                # Style 1: OR-Only targeting
                interests_text = " OR ".join([i.name for i in column.interests])

            # Add review flag if needed
            if column.needs_review:
                interests_text += f"\n\n⚠️ NEEDS REVIEW: {column.review_reason}"

            # Add audience size if available
            if (
                column.estimated_reach_lower is not None
                and column.estimated_reach_upper is not None
            ):
                interests_text += f"\n\nReach: {column.estimated_reach_lower:,} - {column.estimated_reach_upper:,}"

            # Add to data
            data.append(
                {
                    "Column ID": column.column_id,
                    "Column Name": column.column_name,
                    "Targeting Style": column.targeting_style.value,
                    "Interests": interests_text,
                    "Interest Count": len(column.interests)
                    + (1 if column.narrow_interest else 0),
                    "Needs Review": "YES" if column.needs_review else "NO",
                }
            )

        # Create the DataFrame
        df = pd.DataFrame(data)

        # Create a second DataFrame for the interest details
        details_data = []

        for column in targeting_sheet.columns:
            for interest in column.interests:
                details_data.append(
                    {
                        "Column ID": column.column_id,
                        "Column Name": column.column_name,
                        "Interest ID": interest.id,
                        "Interest Name": interest.name,
                        "Audience Size Lower": interest.audience_size_lower_bound,
                        "Audience Size Upper": interest.audience_size_upper_bound,
                        "Topic": interest.topic,
                        "Path": " > ".join(interest.path) if interest.path else "",
                        "Description": interest.description,
                        "Role": "OR Interest",
                    }
                )

            if column.narrow_interest:
                details_data.append(
                    {
                        "Column ID": column.column_id,
                        "Column Name": column.column_name,
                        "Interest ID": column.narrow_interest.id,
                        "Interest Name": column.narrow_interest.name,
                        "Audience Size Lower": column.narrow_interest.audience_size_lower_bound,
                        "Audience Size Upper": column.narrow_interest.audience_size_upper_bound,
                        "Topic": column.narrow_interest.topic,
                        "Path": (
                            " > ".join(column.narrow_interest.path)
                            if column.narrow_interest.path
                            else ""
                        ),
                        "Description": column.narrow_interest.description,
                        "Role": "Narrow Interest",
                    }
                )

        details_df = pd.DataFrame(details_data)

        # Create a summary DataFrame
        summary_data = {
            "Created At": targeting_sheet.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "Country": targeting_sheet.country_code,
            "Age Range": f"{targeting_sheet.age_min}-{targeting_sheet.age_max}",
            "Total Interests": targeting_sheet.total_interests_found,
            "Total Suggestions": targeting_sheet.total_suggestions_found,
            "Columns Needing Review": sum(
                1 for c in targeting_sheet.columns if c.needs_review
            ),
        }

        summary_df = pd.DataFrame([summary_data])

        # Create a writer to save the Excel file
        with pd.ExcelWriter(output_path, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="Targeting Sheet", index=False)
            details_df.to_excel(writer, sheet_name="Interest Details", index=False)
            summary_df.to_excel(writer, sheet_name="Summary", index=False)

        logger.info(f"Excel file generated successfully at {output_path}")
        return output_path

    def _assign_targeting_styles(self) -> Dict[int, TargetingStyle]:
        """
        Assign targeting styles to columns following the 4-4-4 rule.

        Returns:
            Dictionary mapping column IDs to targeting styles
        """
        # Get the default style assignments from settings
        default_styles = {
            column["id"]: column["default_style"]
            for column in settings.TARGETING_COLUMNS
        }

        # Convert to TargetingStyle enum
        style_map = {
            1: TargetingStyle.OR_ONLY,
            2: TargetingStyle.NARROWED,
            3: TargetingStyle.SINGLE_INTEREST,
        }

        style_assignments = {
            column_id: style_map[style_id]
            for column_id, style_id in default_styles.items()
        }

        # Count styles to ensure 4-4-4 distribution
        style_counts = {
            TargetingStyle.OR_ONLY: 0,
            TargetingStyle.NARROWED: 0,
            TargetingStyle.SINGLE_INTEREST: 0,
        }

        for style in style_assignments.values():
            style_counts[style] += 1

        # Adjust if needed to maintain 4-4-4 balance
        if all(count == 4 for count in style_counts.values()):
            # Already balanced
            return style_assignments

        # Find styles that need more columns
        styles_needed = {
            style: 4 - count for style, count in style_counts.items() if count < 4
        }

        # Find styles that have too many columns
        styles_excess = {
            style: count - 4 for style, count in style_counts.items() if count > 4
        }

        # Adjust by reassigning some columns
        for excess_style, excess_count in styles_excess.items():
            for needed_style, needed_count in styles_needed.items():
                if excess_count <= 0 or needed_count <= 0:
                    continue

                # Find columns with excess style that can be changed
                columns_to_change = [
                    column_id
                    for column_id, style in style_assignments.items()
                    if style == excess_style
                ]

                # Randomly select columns to change
                random.shuffle(columns_to_change)
                columns_to_change = columns_to_change[: min(excess_count, needed_count)]

                # Change the styles
                for column_id in columns_to_change:
                    style_assignments[column_id] = needed_style
                    excess_count -= 1
                    needed_count -= 1

                # Update the counts
                styles_needed[needed_style] = needed_count
                styles_excess[excess_style] = excess_count

        return style_assignments

    def _find_narrow_interest(
        self,
        classified_interests: Dict[int, List[InterestItem]],
        current_column_id: int,
        first_layer_interests: List[InterestItem],
    ) -> Optional[InterestItem]:
        """
        Find a suitable narrow interest for a column.

        Args:
            classified_interests: Dictionary mapping column IDs to lists of interests
            current_column_id: ID of the current column
            first_layer_interests: Interests already selected for the first layer

        Returns:
            An interest to use as a narrow interest, or None if not found
        """
        # Collect all interests from other columns that aren't already assigned
        candidate_interests = []

        for column_id, interests in classified_interests.items():
            if column_id == current_column_id:
                continue

            for interest in interests:
                if interest.id not in self.assigned_interests:
                    # Skip interests that are too small
                    if (interest.audience_size_upper_bound or 0) < 1000000:
                        continue

                    candidate_interests.append(interest)

        if not candidate_interests:
            # If no suitable interests from other columns, try to use a broad interest
            # from the core column (Column 8)
            core_interests = classified_interests.get(8, [])
            for interest in core_interests:
                if interest.id not in self.assigned_interests:
                    return interest

            # If still no suitable interest, return None
            return None

        # Sort candidates by audience size (descending)
        candidate_interests.sort(
            key=lambda i: i.audience_size_upper_bound or 0, reverse=True
        )

        # Return the largest candidate
        return candidate_interests[0]
