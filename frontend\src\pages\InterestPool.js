import React, { useState, useEffect } from 'react';
import '../styles/pages/interest-pool.css';

const InterestPool = () => {
    const [selectAll, setSelectAll] = useState(false);
    const [selectedItems, setSelectedItems] = useState(new Set());
    const [nameFilter, setNameFilter] = useState('');
    const [pathFilter, setPathFilter] = useState('');
    const [interests, setInterests] = useState([
        {
            id: '6003288647527',
            name: 'Projectors (consumer electronics)',
            audience: 'N/A',
            type: 'interests',
            path: ['Interests', 'Technology', 'Consumer electronics', 'Projectors']
        },
        {
            id: '6002839798079',
            name: 'Slide projector (consumer electronics)',
            audience: 'N/A',
            type: 'interests',
            path: ['Interests', 'Additional Interests', 'Slide projector']
        }
    ]);

    const stats = {
        totalInterests: interests.length,
        totalAudience: 0,
        interestTypes: 1,
        categories: 2
    };

    const handleSelectAll = (checked) => {
        setSelectAll(checked);
        if (checked) {
            setSelectedItems(new Set(interests.map(i => i.id)));
        } else {
            setSelectedItems(new Set());
        }
    };

    const handleSelectItem = (id) => {
        const newSelected = new Set(selectedItems);
        if (newSelected.has(id)) {
            newSelected.delete(id);
        } else {
            newSelected.add(id);
        }
        setSelectedItems(newSelected);
        setSelectAll(newSelected.size === interests.length);
    };

    const handleCopyId = (id) => {
        navigator.clipboard.writeText(id);
    };

    const handleRemoveItem = (id) => {
        setInterests(interests.filter(i => i.id !== id));
        const newSelected = new Set(selectedItems);
        newSelected.delete(id);
        setSelectedItems(newSelected);
    };

    const filteredInterests = interests.filter(interest => {
        const nameMatch = interest.name.toLowerCase().includes(nameFilter.toLowerCase());
        const pathMatch = interest.path.join(' ').toLowerCase().includes(pathFilter.toLowerCase());
        return nameMatch && pathMatch;
    });

    return (
        <>
            {/* Header */}
            <header className="header">
                <div className="header-content">
                    <a href="#" className="logo">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <circle cx="12" cy="12" r="6"></circle>
                            <circle cx="12" cy="12" r="2"></circle>
                        </svg>
                        TargetWise
                    </a>
                    
                    <nav className="nav-menu">
                        <a href="#" className="nav-link">Home</a>
                        <a href="#" className="nav-link active">Micro-Tools</a>
                        <a href="#" className="nav-link">Documentation</a>
                        <a href="#" className="nav-link">Pricing</a>
                        <a href="#" className="nav-link">Admin</a>
                    </nav>
                    
                    <div className="header-actions">
                        <button className="theme-toggle">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <circle cx="12" cy="12" r="5"></circle>
                                <line x1="12" y1="1" x2="12" y2="3"></line>
                                <line x1="12" y1="21" x2="12" y2="23"></line>
                                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                                <line x1="1" y1="12" x2="3" y2="12"></line>
                                <line x1="21" y1="12" x2="23" y2="12"></line>
                                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                            </svg>
                        </button>
                        <a href="#" className="btn btn-outline">Log In</a>
                        <a href="#" className="btn btn-primary">Sign Up Free</a>
                    </div>
                </div>
            </header>

            {/* Main Container */}
            <div className="main-container">
                {/* Sidebar */}
                <aside className="sidebar">
                    <h3 className="sidebar-title">Micro-Tools</h3>
                    <nav className="sidebar-menu">
                        <a href="#" className="sidebar-item">
                            <svg className="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <rect x="3" y="3" width="7" height="7"></rect>
                                <rect x="14" y="3" width="7" height="7"></rect>
                                <rect x="14" y="14" width="7" height="7"></rect>
                                <rect x="3" y="14" width="7" height="7"></rect>
                            </svg>
                            Dashboard
                        </a>
                        <a href="#" className="sidebar-item">
                            <svg className="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                            Interest Search
                        </a>
                        <a href="#" className="sidebar-item">
                            <svg className="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>
                            </svg>
                            Interest Suggestions
                        </a>
                        <a href="#" className="sidebar-item">
                            <svg className="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M3 3v18h18"></path>
                                <path d="m19 9-5 5-4-4-3 3"></path>
                            </svg>
                            Taxonomy Browser
                        </a>
                        <a href="#" className="sidebar-item active">
                            <svg className="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"></path>
                            </svg>
                            Interest Pool
                        </a>
                    </nav>
                </aside>

                {/* Content */}
                <main className="content">
                    {/* Page Header */}
                    <div className="page-header">
                        <div className="page-header-top">
                            <div>
                                <h1 className="page-title">
                                    <div className="page-icon">📁</div>
                                    Interest Pool
                                </h1>
                                <p className="page-subtitle">Manage your collected interests</p>
                            </div>
                            <div className="page-actions">
                                <button className="btn btn-secondary">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <path d="M9 18l6-6-6-6"></path>
                                    </svg>
                                    Back to Tools
                                </button>
                                <button className="btn btn-primary">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                                        <line x1="4" y1="22" x2="4" y2="15"></line>
                                    </svg>
                                    Main App
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Stats Cards */}
                    <div className="stats-grid">
                        <div className="stat-card">
                            <div className="stat-icon">📊</div>
                            <div className="stat-value">{stats.totalInterests}</div>
                            <div className="stat-label">Total Interests</div>
                        </div>
                        <div className="stat-card">
                            <div className="stat-icon">👥</div>
                            <div className="stat-value">{stats.totalAudience}</div>
                            <div className="stat-label">Total Audience</div>
                        </div>
                        <div className="stat-card">
                            <div className="stat-icon">🏷️</div>
                            <div className="stat-value">{stats.interestTypes}</div>
                            <div className="stat-label">Interest Types</div>
                        </div>
                        <div className="stat-card">
                            <div className="stat-icon">📁</div>
                            <div className="stat-value">{stats.categories}</div>
                            <div className="stat-label">Categories</div>
                        </div>
                    </div>

                    {/* Collection Info */}
                    <div className="collection-info">
                        <div className="info-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <path d="M12 6v6l4 2"></path>
                            </svg>
                        </div>
                        <div className="info-text">
                            This page shows all interests you've collected from the micro-tools. You can export them to CSV or send them to the main application for use in your Facebook ad campaigns.
                        </div>
                    </div>

                    {/* Actions Bar */}
                    <div className="actions-bar">
                        <div className="action-buttons">
                            <button className="btn btn-success">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="7 10 12 15 17 10"></polyline>
                                    <line x1="12" y1="15" x2="12" y2="3"></line>
                                </svg>
                                Export to CSV
                            </button>
                            <button className="btn btn-secondary">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <polyline points="3 6 5 6 21 6"></polyline>
                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                </svg>
                                Clear All
                            </button>
                            <button className="btn btn-primary">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <line x1="22" y1="2" x2="11" y2="13"></line>
                                    <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                                </svg>
                                Send to Main App
                            </button>
                        </div>
                        <div className="selection-info">
                            <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                <input 
                                    type="checkbox" 
                                    className="checkbox"
                                    checked={selectAll}
                                    onChange={(e) => handleSelectAll(e.target.checked)}
                                />
                                <span>Select All</span>
                            </label>
                            <span>{selectedItems.size} selected</span>
                        </div>
                    </div>

                    {/* Filters */}
                    <div className="filters-section">
                        <div className="filters-header">
                            <h3 className="filters-title">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                                </svg>
                                Filters
                            </h3>
                            <button className="btn btn-clear-filters">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                                Clear Filters
                            </button>
                        </div>
                        <div className="filters-grid">
                            <div className="filter-group">
                                <label className="filter-label">Name</label>
                                <input 
                                    type="text" 
                                    className="filter-input" 
                                    placeholder="Filter by name..."
                                    value={nameFilter}
                                    onChange={(e) => setNameFilter(e.target.value)}
                                />
                            </div>
                            <div className="filter-group">
                                <label className="filter-label">Audience Size</label>
                                <div className="range-inputs">
                                    <input type="text" className="filter-input range-input" placeholder="Min" />
                                    <span className="range-separator">to</span>
                                    <input type="text" className="filter-input range-input" placeholder="Max" />
                                </div>
                            </div>
                            <div className="filter-group">
                                <label className="filter-label">Type</label>
                                <select className="filter-input">
                                    <option>All Types</option>
                                    <option>Interests</option>
                                    <option>Behaviors</option>
                                    <option>Demographics</option>
                                </select>
                            </div>
                            <div className="filter-group">
                                <label className="filter-label">Path</label>
                                <input 
                                    type="text" 
                                    className="filter-input" 
                                    placeholder="Filter by path..."
                                    value={pathFilter}
                                    onChange={(e) => setPathFilter(e.target.value)}
                                />
                            </div>
                        </div>
                    </div>

                    {/* Table */}
                    <div className="table-container">
                        <div className="table-header">
                            <h3 className="table-title">Collected Interests</h3>
                            <span className="stat-label">{filteredInterests.length} items</span>
                        </div>
                        <div className="table-wrapper">
                            <table>
                                <thead>
                                    <tr>
                                        <th style={{ width: '50px' }}>
                                            <input 
                                                type="checkbox" 
                                                className="checkbox"
                                                checked={selectAll}
                                                onChange={(e) => handleSelectAll(e.target.checked)}
                                            />
                                        </th>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Audience Size</th>
                                        <th>Type</th>
                                        <th>Path</th>
                                        <th style={{ width: '100px' }}>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {filteredInterests.map(interest => (
                                        <tr key={interest.id}>
                                            <td>
                                                <div className="checkbox-wrapper">
                                                    <input 
                                                        type="checkbox" 
                                                        className="checkbox row-checkbox"
                                                        checked={selectedItems.has(interest.id)}
                                                        onChange={() => handleSelectItem(interest.id)}
                                                    />
                                                </div>
                                            </td>
                                            <td className="interest-id">{interest.id}</td>
                                            <td className="interest-name">{interest.name}</td>
                                            <td>
                                                <span className="audience-badge">{interest.audience}</span>
                                            </td>
                                            <td>
                                                <span className="interest-type">{interest.type}</span>
                                            </td>
                                            <td>
                                                <div className="path-breadcrumb">
                                                    {interest.path.map((segment, index) => (
                                                        <React.Fragment key={index}>
                                                            <span>{segment}</span>
                                                            {index < interest.path.length - 1 && (
                                                                <span className="path-separator">›</span>
                                                            )}
                                                        </React.Fragment>
                                                    ))}
                                                </div>
                                            </td>
                                            <td>
                                                <div className="action-buttons">
                                                    <button 
                                                        className="action-btn" 
                                                        title="Copy ID"
                                                        onClick={() => handleCopyId(interest.id)}
                                                    >
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                                        </svg>
                                                    </button>
                                                    <button 
                                                        className="action-btn delete-btn" 
                                                        title="Remove"
                                                        onClick={() => handleRemoveItem(interest.id)}
                                                    >
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                            <polyline points="3 6 5 6 21 6"></polyline>
                                                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </main>
            </div>
        </>
    );
};

export default InterestPool;