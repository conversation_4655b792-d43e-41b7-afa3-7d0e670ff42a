import pytest
from api.services.interest_classifier import InterestClassifier
from api.schemas.schemas import InterestItem


def test_classify_by_size():
    classifier = InterestClassifier()

    small_interest = InterestItem(id='1', name='Small Interest', audience_size_upper_bound=500_000)
    large_interest = InterestItem(id='2', name='Large Interest', audience_size_upper_bound=1_500_000)

    assert classifier._classify_by_size(small_interest) == 1
    assert classifier._classify_by_size(large_interest) == 2
