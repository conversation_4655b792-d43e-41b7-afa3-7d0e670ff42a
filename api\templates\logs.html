<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TargetWise - Log Viewer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --color-primary: #2563eb;
            --color-primary-hover: #1d4ed8;
            --color-success: #10b981;
            --color-danger: #ef4444;
            --color-warning: #f59e0b;
            --color-info: #3b82f6;
            --color-text: #1f2937;
            --color-text-secondary: #6b7280;
            --color-border: #e5e7eb;
            --color-bg-light: #f9fafb;
            --light-bg: #f8f9fa;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            background-color: var(--color-bg-light);
            color: var(--color-text);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            margin-bottom: 2rem;
        }
        
        .tw-btn {
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s;
            display: inline-block;
            border: 1px solid transparent;
            cursor: pointer;
        }
        
        .tw-btn-outline-primary {
            border-color: var(--color-primary);
            color: var(--color-primary);
            background: white;
        }
        
        .tw-btn-outline-primary:hover {
            background-color: var(--color-primary);
            color: white;
        }
        
        .tw-btn-danger {
            background-color: var(--color-danger);
            color: white;
        }
        
        .tw-btn-primary {
            background-color: var(--color-primary);
            color: white;
        }
        
        .log-container {
            background-color: white;
            border-radius: 8px;
            border: 1px solid var(--color-border);
            height: calc(100vh - 300px);
            min-height: 400px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9rem;
            padding: 0;
        }
        
        .log-toolbar {
            padding: 15px;
            border-bottom: 1px solid var(--color-border);
            background-color: var(--light-bg);
        }
        
        .log-content {
            padding: 15px;
        }
        
        .log-entry {
            padding: 4px 8px;
            margin-bottom: 4px;
            border-radius: 4px;
            display: flex;
            align-items: flex-start;
        }
        
        .log-entry:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        .log-entry .timestamp {
            color: var(--color-text-secondary);
            margin-right: 10px;
            white-space: nowrap;
        }
        
        .log-entry .source {
            color: var(--color-info);
            margin-right: 10px;
            white-space: nowrap;
            font-weight: 500;
        }
        
        .log-entry .level {
            margin-right: 10px;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            white-space: nowrap;
        }
        
        .log-entry .level.info {
            background-color: #e0f2fe;
            color: #0369a1;
        }
        
        .log-entry .level.warning {
            background-color: #fef3c7;
            color: #d97706;
        }
        
        .log-entry .level.error {
            background-color: #fee2e2;
            color: #dc2626;
        }
        
        .log-entry .level.debug {
            background-color: #e5e7eb;
            color: #374151;
        }
        
        .log-entry .message {
            flex: 1;
            word-break: break-word;
        }
        
        .log-stats {
            display: flex;
            gap: 20px;
            align-items: center;
            font-size: 0.9rem;
        }
        
        .log-stats .stat {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .log-stats .stat .count {
            font-weight: 600;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: var(--color-text-secondary);
        }
        
        .loading .spinner {
            margin-bottom: 10px;
        }
        
        select.form-select {
            padding: 0.375rem 2rem 0.375rem 0.75rem;
            border: 1px solid var(--color-border);
            border-radius: 0.375rem;
        }
        
        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header d-flex justify-content-between align-items-center">
            <div>
                <h1>Log Viewer</h1>
                <p class="text-muted mb-0">Last updated: <span id="last-updated">{{ now.strftime('%Y-%m-%d %H:%M:%S') }}</span></p>
            </div>
            <div>
                <a href="/" class="tw-btn tw-btn-outline-primary">Back to API</a>
                <a href="/admin" class="tw-btn tw-btn-outline-primary">Admin Dashboard</a>
            </div>
        </div>

        <div class="log-container">
            <div class="log-toolbar">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <select class="form-select" id="level-filter">
                            <option value="all">All Levels</option>
                            <option value="info">Info</option>
                            <option value="warning">Warning</option>
                            <option value="error">Error</option>
                            <option value="debug">Debug</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control" id="search-filter" placeholder="Search logs...">
                    </div>
                    <div class="col-md-3">
                        <label class="checkbox-label">
                            <input type="checkbox" id="auto-refresh" checked>
                            <span>Auto-refresh (5s)</span>
                        </label>
                    </div>
                    <div class="col-md-3 text-end">
                        <button class="tw-btn tw-btn-primary" onclick="refreshLogs()">Refresh</button>
                        <button class="tw-btn tw-btn-danger" onclick="clearLogs()">Clear Logs</button>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col">
                        <div class="log-stats">
                            <div class="stat">
                                <span>Total:</span>
                                <span class="count" id="total-count">0</span>
                            </div>
                            <div class="stat">
                                <span>Info:</span>
                                <span class="count text-info" id="info-count">0</span>
                            </div>
                            <div class="stat">
                                <span>Warning:</span>
                                <span class="count text-warning" id="warning-count">0</span>
                            </div>
                            <div class="stat">
                                <span>Error:</span>
                                <span class="count text-danger" id="error-count">0</span>
                            </div>
                            <div class="stat">
                                <span>Debug:</span>
                                <span class="count text-secondary" id="debug-count">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="log-content" id="log-content">
                <div class="loading">
                    <div class="spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <p>Loading logs...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let logs = [];
        let autoRefreshInterval;
        
        function formatDate(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString('en-US', { 
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
        
        function renderLogs() {
            const levelFilter = document.getElementById('level-filter').value;
            const searchFilter = document.getElementById('search-filter').value.toLowerCase();
            const logContent = document.getElementById('log-content');
            
            let filteredLogs = logs;
            
            if (levelFilter !== 'all') {
                filteredLogs = filteredLogs.filter(log => log.level === levelFilter.toUpperCase());
            }
            
            if (searchFilter) {
                filteredLogs = filteredLogs.filter(log => 
                    log.message.toLowerCase().includes(searchFilter) ||
                    (log.source && log.source.toLowerCase().includes(searchFilter))
                );
            }
            
            // Update counts
            const counts = {
                total: logs.length,
                info: logs.filter(l => l.level === 'INFO').length,
                warning: logs.filter(l => l.level === 'WARNING').length,
                error: logs.filter(l => l.level === 'ERROR').length,
                debug: logs.filter(l => l.level === 'DEBUG').length
            };
            
            document.getElementById('total-count').textContent = counts.total;
            document.getElementById('info-count').textContent = counts.info;
            document.getElementById('warning-count').textContent = counts.warning;
            document.getElementById('error-count').textContent = counts.error;
            document.getElementById('debug-count').textContent = counts.debug;
            
            // Render logs
            if (filteredLogs.length === 0) {
                logContent.innerHTML = '<p class="text-center text-muted my-5">No logs found</p>';
                return;
            }
            
            const html = filteredLogs.map(log => `
                <div class="log-entry">
                    <span class="timestamp">${formatDate(log.timestamp)}</span>
                    ${log.source ? `<span class="source">[${log.source}]</span>` : ''}
                    <span class="level ${log.level.toLowerCase()}">${log.level}</span>
                    <span class="message">${escapeHtml(log.message)}</span>
                </div>
            `).join('');
            
            logContent.innerHTML = html;
            
            // Auto-scroll to bottom
            logContent.scrollTop = logContent.scrollHeight;
        }
        
        function escapeHtml(text) {
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, m => map[m]);
        }
        
        async function refreshLogs() {
            try {
                const response = await fetch('/api/logs/recent');
                const data = await response.json();
                
                if (data.success) {
                    logs = data.logs;
                    renderLogs();
                    document.getElementById('last-updated').textContent = new Date().toLocaleString();
                }
            } catch (error) {
                console.error('Error fetching logs:', error);
            }
        }
        
        async function clearLogs() {
            if (!confirm('Are you sure you want to clear all logs?')) {
                return;
            }
            
            try {
                const response = await fetch('/api/logs/clear', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    logs = [];
                    renderLogs();
                    alert('Logs cleared successfully');
                }
            } catch (error) {
                console.error('Error clearing logs:', error);
                alert('Failed to clear logs');
            }
        }
        
        // Event listeners
        document.getElementById('level-filter').addEventListener('change', renderLogs);
        document.getElementById('search-filter').addEventListener('input', renderLogs);
        
        document.getElementById('auto-refresh').addEventListener('change', function(e) {
            if (e.target.checked) {
                autoRefreshInterval = setInterval(refreshLogs, 5000);
            } else {
                clearInterval(autoRefreshInterval);
            }
        });
        
        // Initial load
        refreshLogs();
        
        // Set up auto-refresh
        if (document.getElementById('auto-refresh').checked) {
            autoRefreshInterval = setInterval(refreshLogs, 5000);
        }
    </script>
</body>
</html>