"""
Notification API routes for the TargetWise application.
Provides a server-sent events (SSE) endpoint for real-time notifications.
"""
import asyncio
import json
import logging
from datetime import datetime
from typing import List

from fastapi import APIRouter, Request
from fastapi.responses import StreamingResponse
from starlette.concurrency import run_in_threadpool

from api.utils.notifications import notify_info

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Store active notification clients
active_clients: List[asyncio.Queue] = []


async def notification_generator(request: Request):
    """
    Generate a stream of server-sent events (SSE) for notifications.
    This is an async generator that yields SSE-formatted events.
    """
    # Create a queue for this client
    queue = asyncio.Queue()
    active_clients.append(queue)
    
    notify_info("New notification client connected", context="sse")
    
    # Initial keepalive
    yield "data: {\"type\":\"info\",\"message\":\"Connected to notification stream\"}\n\n"
    
    try:
        # Continue until client disconnects
        while True:
            # Wait for data, with timeout for regular keepalives
            try:
                data = await asyncio.wait_for(queue.get(), timeout=30.0)
                if data:
                    yield f"data: {json.dumps(data)}\n\n"
            except asyncio.TimeoutError:
                # Keepalive every 30 seconds
                yield ": keepalive\n\n"
                
            # Check if client disconnected
            if await request.is_disconnected():
                break
    finally:
        # Clean up when client disconnects
        if queue in active_clients:
            active_clients.remove(queue)
        notify_info("Notification client disconnected", context="sse")


@router.get("/notifications/stream")
async def notifications_stream(request: Request):
    """
    Server-sent events endpoint for receiving real-time notifications.
    """
    return StreamingResponse(
        notification_generator(request),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # Disable nginx buffering
        },
    )


async def broadcast_notification(
    type_name: str, 
    message: str, 
    context: str = None, 
    title: str = None, 
    duration: int = 5000
):
    """
    Broadcast a notification to all connected clients.
    
    Args:
        type_name: Type of notification (success, error, warning, info, debug)
        message: Message content
        context: Additional context information
        title: Optional title override
        duration: How long the notification should display (in ms)
    """
    if not active_clients:
        return
    
    data = {
        "type": type_name,
        "message": message,
        "timestamp": datetime.now().isoformat(),
    }
    
    if context:
        data["context"] = context
    if title:
        data["title"] = title
    if duration != 5000:
        data["duration"] = duration
    
    # Queue notification for all active clients
    for queue in active_clients:
        await queue.put(data)
