# // DOC: docs/micro-tools/search-bulk-feature.md
"""
Utility functions for asynchronous operations.
"""

import asyncio
import logging
from typing import Any, Awaitable, Callable, List, TypeVar

logger = logging.getLogger(__name__)

T = TypeVar("T")
U = TypeVar("U")


async def gather_with_concurrency(n: int, *tasks: Awaitable[T]) -> List[T]:
    """
    Run tasks concurrently with a limit on the number of concurrent tasks.

    Args:
        n: Maximum number of concurrent tasks
        tasks: Tasks to run

    Returns:
        List of results from the tasks
    """
    semaphore = asyncio.Semaphore(n)

    async def sem_task(task):
        async with semaphore:
            return await task

    return await asyncio.gather(*(sem_task(task) for task in tasks))


async def process_items_concurrently(
    items: List[T],
    process_func: Callable[[T], Awaitable[U]],
    concurrency: int = 5,
    flatten_results: bool = False,
) -> List[U]:
    """
    Process a list of items concurrently with a limit on the number of concurrent tasks.

    Args:
        items: List of items to process
        process_func: Function to process each item
        concurrency: Maximum number of concurrent tasks
        flatten_results: Whether to flatten the results (if each task returns a list)

    Returns:
        List of results from processing the items
    """
    if not items:
        return []

    semaphore = asyncio.Semaphore(concurrency)

    async def bounded_process(item):
        async with semaphore:
            try:
                return await process_func(item)
            except Exception as e:
                logger.error(f"Error processing item {item}: {str(e)}")
                return None

    # Create tasks for all items
    tasks = [bounded_process(item) for item in items]

    # Execute all tasks in parallel and gather results
    results = await asyncio.gather(*tasks)

    # Filter out None results
    filtered_results = [result for result in results if result is not None]

    # Flatten results if needed
    if flatten_results:
        flattened = []
        for result in filtered_results:
            if isinstance(result, list):
                flattened.extend(result)
            else:
                flattened.append(result)
        return flattened

    return filtered_results


async def retry_with_backoff(
    func: Callable[..., Awaitable[T]],
    *args: Any,
    max_retries: int = 3,
    initial_backoff: float = 1.0,
    max_backoff: float = 32.0,
    **kwargs: Any,
) -> T:
    """
    Retry a function with exponential backoff.

    Args:
        func: Function to retry
        args: Arguments for the function
        max_retries: Maximum number of retries
        initial_backoff: Initial backoff time in seconds
        max_backoff: Maximum backoff time in seconds
        kwargs: Keyword arguments for the function

    Returns:
        Result of the function

    Raises:
        Exception: If the function fails after all retries
    """
    retries = 0
    while True:
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            retries += 1
            if retries > max_retries:
                logger.error(f"Failed after {max_retries} retries: {str(e)}")
                raise

            backoff_time = min(initial_backoff * (2 ** (retries - 1)), max_backoff)
            logger.warning(
                f"Retry {retries}/{max_retries} after {backoff_time}s: {str(e)}"
            )
            await asyncio.sleep(backoff_time)
