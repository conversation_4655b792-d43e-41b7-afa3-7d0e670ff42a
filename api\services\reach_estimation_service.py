"""Reach estimation and validation Facebook API operations."""

import json
import logging
from typing import Any, Dict, List, Optional

import httpx

from .facebook_base import FacebookBaseService
from api.schemas import InterestItem, ReachEstimateResponse, TargetingValidationResponse
from api.utils.async_utils import process_items_concurrently
from api.utils.facebook_utils import create_targeting_spec, interest_from_api_response

logger = logging.getLogger(__name__)


class ReachEstimationService(FacebookBaseService):
    async def estimate_reach(
        self,
        interests: List[InterestItem],
        narrow_interest: Optional[InterestItem] = None,
        country_code: str = "US",
        age_min: int = 18,
        age_max: int = 65,
    ) -> ReachEstimateResponse:
        """Estimate the reach for a set of interests."""
        targeting_spec = create_targeting_spec(
            interests=interests,
            narrow_interest=narrow_interest,
            country_code=country_code,
            age_min=age_min,
            age_max=age_max,
        )
        spec_hash = json.dumps(targeting_spec, sort_keys=True)
        cache_key = f"reach:{spec_hash}:{country_code}"

        endpoint = f"act_{self.ad_account_id}/delivery_estimate"
        params = {
            "access_token": self.access_token,
            "optimization_goal": "REACH",
            "targeting_spec": json.dumps(targeting_spec),
        }

        def process_reach_response(data: Dict[str, Any]) -> ReachEstimateResponse:
            if "data" in data and len(data["data"]) > 0:
                estimate = data["data"][0]
                return ReachEstimateResponse(
                    users_lower_bound=estimate.get("estimate_dau", {}).get("lower_bound", 0),
                    users_upper_bound=estimate.get("estimate_dau", {}).get("upper_bound", 0),
                    estimate_ready=estimate.get("estimate_ready", False),
                    estimate_status=estimate.get("estimate_status", 0),
                )
            return ReachEstimateResponse(
                users_lower_bound=0,
                users_upper_bound=0,
                estimate_ready=False,
                estimate_status=0,
            )

        success, result, error = await self.api_handler.make_api_request(
            endpoint=endpoint,
            params=params,
            cache_key=cache_key,
            cache_ttl=24 * 3600,
            score_cost=2,
            mock_data_func=None,
            response_processor=process_reach_response,
        )

        if success:
            logger.info(
                "Got reach estimate: %s - %s",
                result.users_lower_bound,
                result.users_upper_bound,
            )
            return result
        logger.error("Error estimating reach: %s", error)
        return ReachEstimateResponse(
            users_lower_bound=0,
            users_upper_bound=0,
            estimate_ready=False,
            estimate_status=0,
        )

    async def batch_estimate_reach(
        self, targeting_specs: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Batch fetch delivery estimates for multiple targeting specs."""
        batch_data = []
        for spec in targeting_specs:
            batch_data.append(
                {
                    "method": "POST",
                    "relative_url": "delivery_estimate",
                    "body": f"optimization_goal=REACH&targeting_spec={json.dumps(spec)}",
                }
            )
        params = {"access_token": self.access_token, "batch": json.dumps(batch_data)}

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{self.base_url}/", params=params)
            if response.status_code == 200:
                batch_results = response.json()
                processed_results = []
                for result in batch_results:
                    if result.get("code") == 200 and result.get("body"):
                        try:
                            body_data = json.loads(result["body"])
                            processed_results.append({"success": True, "data": body_data.get("data", [{}])[0]})
                        except Exception as e:
                            processed_results.append({"success": False, "error": f"Failed to parse result: {str(e)}"})
                    else:
                        processed_results.append(
                            {
                                "success": False,
                                "error": f"API error: {result.get('code')} - {result.get('body')}",
                            }
                        )
                return processed_results
            logger.error("Batch request failed: %s - %s", response.status_code, response.text)
            return [{"success": False, "error": "Batch request failed"}] * len(targeting_specs)
        except Exception as e:
            logger.error("Exception in batch request: %s", str(e))
            return [{"success": False, "error": str(e)}] * len(targeting_specs)

    async def test_connection(self) -> Dict[str, Any]:
        """Test the connection to the Facebook Marketing API."""
        endpoint = f"act_{self.ad_account_id}"
        params = {
            "fields": "name,account_status,currency,timezone_name",
            "access_token": self.access_token,
        }

        def process_connection_test(data: Dict[str, Any]) -> Dict[str, Any]:
            return {
                "success": True,
                "account_name": data.get("name", "Unknown"),
                "account_status": data.get("account_status", 0),
                "currency": data.get("currency", "USD"),
                "timezone": data.get("timezone_name", "America/Los_Angeles"),
                "message": "Connection successful",
            }

        success, result, error = await self.api_handler.make_api_request(
            endpoint=endpoint,
            params=params,
            cache_key="connection_test",
            cache_ttl=300,
            score_cost=1,
            mock_data_func=None,
            response_processor=process_connection_test,
        )

        if success:
            logger.info(
                "Connection test successful for account: %s", result.get("account_name")
            )
            return result
        logger.error("Connection test failed: %s", error)
        return {"success": False, "error": str(error), "message": "Connection failed"}

    async def validate_targeting(
        self,
        interests: List[InterestItem],
        country_code: str = "US",
        age_min: int = 18,
        age_max: int = 65,
    ) -> TargetingValidationResponse:
        """Validate targeting parameters."""
        targeting_spec = create_targeting_spec(
            interests=interests,
            country_code=country_code,
            age_min=age_min,
            age_max=age_max,
        )
        endpoint = f"act_{self.ad_account_id}/targetingvalidation"
        params = {
            "access_token": self.access_token,
            "targeting_spec": json.dumps(targeting_spec),
        }

        def process_validation_response(data: Dict[str, Any]) -> TargetingValidationResponse:
            validated_interests = []
            if "data" in data and isinstance(data["data"], list):
                for item in data["data"]:
                    interest = interest_from_api_response(item)
                    validated_interests.append(interest)
            return TargetingValidationResponse(
                valid=data.get("valid", False),
                interests=validated_interests,
                error=None,
            )

        success, result, error = await self.api_handler.make_api_request(
            endpoint=endpoint,
            params=params,
            cache_key=f"validation:{','.join([i.id for i in interests])}:{country_code}",
            cache_ttl=24 * 3600,
            score_cost=1,
            mock_data_func=None,
            response_processor=process_validation_response,
        )

        if success:
            logger.info("Targeting validation result: %s", result.valid)
            return result
        logger.error("Error validating targeting: %s", error)
        from api.schemas import FacebookAPIError

        return TargetingValidationResponse(
            valid=False,
            interests=[],
            error=FacebookAPIError(
                code=500,
                message="API error occurred during validation",
                type="APIError",
            ),
        )

    async def estimate_and_adjust_reach(
        self,
        targeting_sheet,
        country_code: str = "US",
        age_min: int = 18,
        age_max: int = 65,
    ):
        """Estimate reach for each column in a targeting sheet and adjust."""
        logger.info(
            "Estimating and adjusting reach for targeting sheet with %d columns",
            len(targeting_sheet.columns),
        )
        for column in targeting_sheet.columns:
            if not column.interests:
                continue
            interests = column.interests
            if column.targeting_style == "narrowed":
                if len(interests) >= 2:
                    narrow_interest = interests[0]
                    other_interests = interests[1:]
                    reach = await self.estimate_reach(
                        interests=other_interests,
                        narrow_interest=narrow_interest,
                        country_code=country_code,
                        age_min=age_min,
                        age_max=age_max,
                    )
                    column.audience_size_lower = reach.users_lower_bound
                    column.audience_size_upper = reach.users_upper_bound
            else:
                reach = await self.estimate_reach(
                    interests=interests,
                    country_code=country_code,
                    age_min=age_min,
                    age_max=age_max,
                )
                column.audience_size_lower = reach.users_lower_bound
                column.audience_size_upper = reach.users_upper_bound
        return targeting_sheet
