/* TargetWise Design System - CSS Variables */
/* This file contains all design tokens to ensure consistency and prevent duplication */

:root {
  /* Colors - Primary Palette */
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-primary-light: rgba(37, 99, 235, 0.1);
  --gradient-primary: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  
  /* Colors - Semantic */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-danger: #ef4444;
  --color-info: #3b82f6;
  
  /* Colors - Semantic Gradients */
  --gradient-success: linear-gradient(135deg, #10b981 0%, #34d399 100%);
  --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
  --gradient-danger: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
  --gradient-info: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
  
  /* Colors - Accent */
  --color-purple: #8b5cf6;
  --gradient-purple: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
  
  /* Colors - Neutrals */
  --color-gray-900: #1a1a2e;
  --color-gray-800: #1e293b;
  --color-gray-700: #334155;
  --color-gray-600: #475569;
  --color-gray-500: #64748b;
  --color-gray-400: #94a3b8;
  --color-gray-300: #cbd5e1;
  --color-gray-200: #e2e8f0;
  --color-gray-100: #f1f5f9;
  --color-gray-50: #f8fafc;
  --color-white: #ffffff;
  
  /* Background Gradients for Icons */
  --gradient-bg-blue: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  --gradient-bg-green: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  --gradient-bg-yellow: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  --gradient-bg-pink: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
  --gradient-bg-purple: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
  
  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', monospace;
  
  /* Font Sizes */
  --text-xs: 13px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 28px;
  --text-3xl: 32px;
  --text-4xl: 36px;
  --text-5xl: 48px;
  
  /* Font Weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* Line Heights */
  --leading-tight: 1.2;
  --leading-normal: 1.6;
  --leading-relaxed: 1.8;
  
  /* Spacing Scale (8px base) */
  --space-0: 0;
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;
  
  /* Border Radius */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 10px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-3xl: 20px;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 14px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 12px 32px rgba(0, 0, 0, 0.16);
  --shadow-primary: 0 4px 14px rgba(37, 99, 235, 0.25);
  --shadow-primary-hover: 0 6px 20px rgba(37, 99, 235, 0.35);
  
  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-base: 0.2s ease-out;
  --transition-slow: 0.3s ease-out;
  --ease-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 1, 1);
  
  /* Z-Index Scale */
  --z-base: 0;
  --z-dropdown: 10;
  --z-sticky: 50;
  --z-overlay: 100;
  --z-modal: 200;
  --z-popover: 300;
  --z-tooltip: 400;
  --z-notification: 500;
  
  /* Layout */
  --container-max: 1400px;
  --content-max: 1200px;
  --sidebar-width: 260px;
  --header-height: 72px;
  
  /* Breakpoints (for reference in JS) */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}