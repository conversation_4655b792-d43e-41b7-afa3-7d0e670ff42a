# // DOC: docs/micro-tools/search-bulk-feature.md
"""
Utility functions for Facebook API.
"""

import logging
import os
from typing import Any, Dict, List, Optional

from api.schemas import InterestItem

logger = logging.getLogger(__name__)


def create_targeting_spec(
    interests: List[InterestItem],
    narrow_interest: Optional[InterestItem] = None,
    country_code: str = "US",
    age_min: int = 18,
    age_max: int = 65,
) -> Dict[str, Any]:
    """
    Create a targeting specification for Facebook API.

    Args:
        interests: List of interests to target
        narrow_interest: Interest to narrow the targeting with
        country_code: Country code for targeting
        age_min: Minimum age for targeting
        age_max: Maximum age for targeting

    Returns:
        Targeting specification dictionary
    """
    targeting_spec = {
        "geo_locations": {"countries": [country_code]},
        "age_min": age_min,
        "age_max": age_max,
    }

    # Add interests based on targeting style
    if narrow_interest:
        # Style 2: Narrowed targeting
        targeting_spec["flexible_spec"] = [
            {"interests": [{"id": i.id, "name": i.name} for i in interests]},
            {"interests": [{"id": narrow_interest.id, "name": narrow_interest.name}]},
        ]
    else:
        # Style 1 or 3: OR-Only or Single-Interest
        targeting_spec["flexible_spec"] = [
            {"interests": [{"id": i.id, "name": i.name} for i in interests]}
        ]

    return targeting_spec


def interest_from_api_response(item: Dict[str, Any]) -> InterestItem:
    """
    Create an InterestItem from an API response.

    Args:
        item: API response item

    Returns:
        InterestItem object
    """
    return InterestItem(
        id=item.get("id", ""),
        name=item.get("name", ""),
        audience_size_lower_bound=item.get("audience_size_lower_bound"),
        audience_size_upper_bound=item.get("audience_size_upper_bound"),
        path=item.get("path", []),
        topic=item.get("topic", ""),
        description=item.get("description", ""),
        disambiguation_category=item.get("disambiguation_category", ""),
        type=item.get("type", "interests"),
    )


def interests_from_api_response(data: Dict[str, Any]) -> List[InterestItem]:
    """
    Create a list of InterestItem objects from an API response.

    Args:
        data: API response data

    Returns:
        List of InterestItem objects
    """
    interests = []

    if "data" in data and len(data["data"]) > 0:
        for item in data["data"]:
            interest = interest_from_api_response(item)
            interests.append(interest)

    return interests


def get_mock_interests(interest_names: List[str], testing_only: bool = False) -> List[InterestItem]:
    """
    Generate mock interests for testing purposes only.
    WARNING: This should NEVER be used in production environment.

    Args:
        interest_names: List of interest names
        testing_only: If False, returns empty list in production environment

    Returns:
        List of mock InterestItem objects in testing environment, empty list in production
    """
    # Only return mock data if explicitly requested for testing
    if not testing_only and os.getenv("ENVIRONMENT", "production").lower() != "testing":
        return []

    mock_interests = []

    for i, name in enumerate(interest_names):
        # Generate a deterministic ID based on the name
        import hashlib

        id_hash = hashlib.md5(name.encode()).hexdigest()
        mock_id = f"6{id_hash[:11]}"

        # Generate a deterministic audience size based on the name
        audience_size = (hash(name) % 10000000) + 1000000

        mock_interest = InterestItem(
            id=mock_id,
            name=name,
            audience_size_lower_bound=int(audience_size * 0.8),
            audience_size_upper_bound=int(audience_size * 1.2),
            path=["Interests", "Mock Category", name],
            topic="Mock Topic",
            description=f"Mock description for {name}",
            disambiguation_category="",
            type="interests",
        )

        mock_interests.append(mock_interest)

    return mock_interests


def get_mock_suggestions(seed_interests: List[InterestItem], testing_only: bool = False) -> List[InterestItem]:
    """
    Generate mock suggestions for testing purposes only.
    WARNING: This should NEVER be used in production environment.

    Args:
        seed_interests: List of seed interests
        testing_only: If False, returns empty list in production environment

    Returns:
        List of mock suggestion InterestItem objects in testing environment, empty list in production
    """
    # Only return mock data if explicitly requested for testing
    if not testing_only and os.getenv("ENVIRONMENT", "production").lower() != "testing":
        return []

    mock_suggestions = []

    for seed in seed_interests:
        # Generate 5 mock suggestions for each seed
        for i in range(5):
            suggestion_name = f"{seed.name} Suggestion {i+1}"

            # Generate a deterministic ID based on the name
            import hashlib

            id_hash = hashlib.md5(suggestion_name.encode()).hexdigest()
            mock_id = f"6{id_hash[:11]}"

            # Generate a deterministic audience size based on the name
            audience_size = (hash(suggestion_name) % 5000000) + 500000

            mock_suggestion = InterestItem(
                id=mock_id,
                name=suggestion_name,
                audience_size_lower_bound=int(audience_size * 0.8),
                audience_size_upper_bound=int(audience_size * 1.2),
                path=["Interests", "Mock Category", seed.name, suggestion_name],
                topic="Mock Topic",
                description=f"Mock suggestion for {seed.name}",
                disambiguation_category="",
                type="interests",
            )

            mock_suggestions.append(mock_suggestion)

    return mock_suggestions


def get_mock_interests_by_ids(interest_ids: List[str], testing_only: bool = False) -> List[InterestItem]:
    """
    Generate mock interests for testing purposes only, based on interest IDs.
    WARNING: This should NEVER be used in production environment.

    Args:
        interest_ids: List of interest IDs
        testing_only: If False, returns empty list in production environment

    Returns:
        List of mock InterestItem objects in testing environment, empty list in production
    """
    # Only return mock data if explicitly requested for testing
    if not testing_only and os.getenv("ENVIRONMENT", "production").lower() != "testing":
        return []

    mock_interests = []

    for interest_id in interest_ids:
        # Generate a deterministic name based on the ID
        name = f"Interest {interest_id}"

        # Generate a deterministic audience size based on the ID
        audience_size = (hash(interest_id) % 10000000) + 1000000

        mock_interest = InterestItem(
            id=interest_id,
            name=name,
            audience_size_lower_bound=int(audience_size * 0.8),
            audience_size_upper_bound=int(audience_size * 1.2),
            path=["Interests", "Mock Category", name],
            topic="Mock Topic",
            description=f"Mock description for {name}",
            disambiguation_category="",
            type="interests",
        )

        mock_interests.append(mock_interest)

    return mock_interests


def get_mock_taxonomy(parent_id: Optional[str] = None, testing_only: bool = False) -> List[Dict[str, Any]]:
    """
    Generate mock taxonomy for testing purposes only.
    WARNING: This should NEVER be used in production environment.

    Args:
        parent_id: Parent category ID
        testing_only: If False, returns empty list in production environment

    Returns:
        List of mock taxonomy nodes in testing environment, empty list in production
    """
    # Only return mock data if explicitly requested for testing
    if not testing_only and os.getenv("ENVIRONMENT", "production").lower() != "testing":
        return []
    if parent_id is None:
        # Return top-level categories
        return [
            {
                "id": "6001",
                "name": "Entertainment",
                "type": "adinterestsuggestion",
                "path": ["Interests", "Entertainment"],
                "audience_size": 2000000000,
            },
            {
                "id": "6002",
                "name": "Technology",
                "type": "adinterestsuggestion",
                "path": ["Interests", "Technology"],
                "audience_size": 1500000000,
            },
            {
                "id": "6003",
                "name": "Hobbies",
                "type": "adinterestsuggestion",
                "path": ["Interests", "Hobbies"],
                "audience_size": 1800000000,
            },
        ]
    else:
        # Return subcategories based on parent ID
        if parent_id == "6001":  # Entertainment
            return [
                {
                    "id": "60011",
                    "name": "Movies",
                    "type": "adinterestsuggestion",
                    "path": ["Interests", "Entertainment", "Movies"],
                    "audience_size": 1500000000,
                },
                {
                    "id": "60012",
                    "name": "TV Shows",
                    "type": "adinterestsuggestion",
                    "path": ["Interests", "Entertainment", "TV Shows"],
                    "audience_size": 1400000000,
                },
            ]
        elif parent_id == "6002":  # Technology
            return [
                {
                    "id": "60021",
                    "name": "Gadgets",
                    "type": "adinterestsuggestion",
                    "path": ["Interests", "Technology", "Gadgets"],
                    "audience_size": 1000000000,
                },
                {
                    "id": "60022",
                    "name": "Software",
                    "type": "adinterestsuggestion",
                    "path": ["Interests", "Technology", "Software"],
                    "audience_size": 800000000,
                },
            ]
        else:
            return []
