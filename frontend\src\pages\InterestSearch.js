/**
 * Interest Search Page - Search and discover Facebook interests
 * Based on interest-search-enhanced.html
 */

import { Header, initHeader } from '../components/common/Header.js';
import { Sidebar, initSidebar } from '../components/common/Sidebar.js';
import { interestApi } from '../services/api/interests.js';

export function InterestSearchPage() {
  return `
    ${Header({ currentPage: 'micro-tools' })}
    
    <div class="main-layout">
      ${Sidebar({ currentTool: 'interest-search' })}
      
      <main class="main-content">
        <!-- Page Header -->
        <div class="page-header">
          <div class="page-header-top">
            <div>
              <h1 class="page-title">
                <div class="page-icon">🔍</div>
                Interest Search
              </h1>
              <p class="page-subtitle">Search for Facebook interests and add them to your pool</p>
            </div>
            <div class="page-actions">
              <button class="btn btn-secondary">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                Back to Tools
              </button>
              <button class="btn btn-primary">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                  <line x1="4" y1="22" x2="4" y2="15"></line>
                </svg>
                Main App
              </button>
            </div>
          </div>
        </div>

        <!-- Search Panel -->
        <div class="card mb-6">
          <div class="flex items-center gap-4 mb-6">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
            <h2 class="text-xl font-semibold text-gray-800">Find Interests</h2>
            <div class="tabs">
              <button class="tab active" id="singleTab">Single Search</button>
              <button class="tab" id="bulkTab">Bulk Search</button>
            </div>
          </div>

          <form class="search-form space-y-5" id="searchForm">
            <div class="form-group">
              <label class="form-label">Search Keywords</label>
              <div class="input-group">
                <svg class="input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.35-4.35"></path>
                </svg>
                <input type="text" class="form-input" placeholder="Enter a keyword like 'hiking', 'movies', or 'technology'" id="searchInput">
              </div>
              <p class="text-sm text-gray-400 mt-1">Tip: Use specific keywords to find targeted audience interests</p>
            </div>

            <div class="form-group">
              <label class="form-label">Search Type</label>
              <select class="form-select" id="searchType">
                <option value="adinterest">Interests</option>
                <option value="behaviors">Behaviors</option>
                <option value="demographics">Demographics</option>
                <option value="work_positions">Job Titles</option>
                <option value="work_employers">Employers</option>
              </select>
            </div>

            <div class="flex gap-3">
              <button type="submit" class="btn btn-primary flex-1">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.35-4.35"></path>
                </svg>
                Search
              </button>
              <button type="button" class="btn btn-outline" id="saveSearchBtn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                  <polyline points="17 21 17 13 7 13 7 21"></polyline>
                  <polyline points="7 3 7 8 15 8"></polyline>
                </svg>
                Save Search
              </button>
            </div>
          </form>
        </div>

        <!-- Results Section -->
        <div class="card">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold text-gray-800 flex items-center gap-3">
              Results
              <span class="badge badge-primary" id="resultsCount">0</span>
            </h3>
            <div class="flex gap-2">
              <button class="btn-icon btn-secondary" title="Select All" id="selectAllBtn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <polyline points="9 11 12 14 22 4"></polyline>
                </svg>
              </button>
              <button class="btn-icon btn-secondary" title="Export Selected" id="exportSelectedBtn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
              </button>
              <button class="btn btn-primary" id="cartBtn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5Z"></path>
                </svg>
                Cart <span class="bg-white text-primary px-2 py-1 rounded-full text-sm ml-1" id="cartCount">0</span>
              </button>
            </div>
          </div>

          <div id="resultsContainer">
            <!-- Empty State -->
            <div class="empty-state" id="emptyState">
              <div class="empty-icon">🔍</div>
              <h4 class="empty-title">No results yet</h4>
              <p class="empty-text">Start searching to find Facebook interests for your campaigns</p>
            </div>

            <!-- Results Table -->
            <div class="overflow-x-auto hidden" id="resultsTable">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 50px;">Select</th>
                    <th style="width: 50px;">Favorite</th>
                    <th>Name</th>
                    <th>ID</th>
                    <th>Audience Size</th>
                    <th>Type</th>
                    <th>Path</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody id="resultsBody">
                  <!-- Results will be populated here -->
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Search History -->
        <div class="card mt-6">
          <div class="flex items-center gap-3 mb-5">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
            <h3 class="text-lg font-semibold text-gray-800">Search History</h3>
          </div>
          <div class="space-y-2" id="searchHistory">
            <!-- History items will be populated here -->
          </div>
        </div>
      </main>
    </div>
  `;
}

// Initialize Interest Search page functionality
export function initInterestSearchPage() {
  initHeader();
  initSidebar();
  
  let searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
  let cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
  
  // Update cart count
  updateCartCount();
  
  // Tab switching
  const singleTab = document.getElementById('singleTab');
  const bulkTab = document.getElementById('bulkTab');

  singleTab?.addEventListener('click', () => {
    singleTab.classList.add('active');
    bulkTab.classList.remove('active');
  });

  bulkTab?.addEventListener('click', () => {
    bulkTab.classList.add('active');
    singleTab.classList.remove('active');
  });

  // Form submission
  const searchForm = document.getElementById('searchForm');
  searchForm?.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const searchInput = document.getElementById('searchInput');
    const searchType = document.getElementById('searchType');
    const query = searchInput.value.trim();
    
    if (!query) {
      searchInput.focus();
      return;
    }
    
    // Add to history
    addToSearchHistory(query);
    
    // Show loading
    showLoading();
    
    try {
      const results = await interestApi.search(query, {
        type: searchType.value,
        limit: 100
      });
      
      displayResults(results.data || []);
    } catch (error) {
      console.error('Search failed:', error);
      showError('Search failed. Please try again.');
    } finally {
      hideLoading();
    }
  });

  // Search history functionality
  function addToSearchHistory(query) {
    const historyItem = {
      term: query,
      timestamp: new Date().toISOString()
    };
    
    searchHistory.unshift(historyItem);
    searchHistory = searchHistory.slice(0, 10); // Keep only last 10
    
    localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
    renderSearchHistory();
  }

  function renderSearchHistory() {
    const historyContainer = document.getElementById('searchHistory');
    
    if (searchHistory.length === 0) {
      historyContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No search history yet</p>';
      return;
    }
    
    historyContainer.innerHTML = searchHistory.map(item => `
      <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition cursor-pointer search-history-item" data-term="${item.term}">
        <span class="font-medium text-gray-700">${item.term}</span>
        <span class="text-gray-400 text-sm">${formatTimeAgo(item.timestamp)}</span>
      </div>
    `).join('');
    
    // Add click handlers for history items
    document.querySelectorAll('.search-history-item').forEach(item => {
      item.addEventListener('click', () => {
        const term = item.dataset.term;
        document.getElementById('searchInput').value = term;
      });
    });
  }

  function displayResults(results) {
    const emptyState = document.getElementById('emptyState');
    const resultsTable = document.getElementById('resultsTable');
    const resultsBody = document.getElementById('resultsBody');
    const resultsCount = document.getElementById('resultsCount');
    
    resultsCount.textContent = results.length;
    
    if (results.length === 0) {
      emptyState.classList.remove('hidden');
      resultsTable.classList.add('hidden');
      return;
    }
    
    emptyState.classList.add('hidden');
    resultsTable.classList.remove('hidden');
    
    resultsBody.innerHTML = results.map(item => `
      <tr>
        <td><input type="checkbox" class="checkbox result-checkbox" data-id="${item.id}"></td>
        <td><button class="favorite-btn text-gray-300 hover:text-yellow-500 transition" data-id="${item.id}">⭐</button></td>
        <td class="font-medium text-gray-800">${item.name}</td>
        <td class="font-mono text-gray-600 text-sm">${item.id}</td>
        <td class="font-medium text-green-600">${formatAudienceSize(item.audience_size_lower_bound, item.audience_size_upper_bound)}</td>
        <td><span class="badge badge-primary">${item.type || 'Interest'}</span></td>
        <td class="text-gray-600 text-sm">${formatPath(item.path)}</td>
        <td>
          <div class="flex gap-2">
            <button class="btn btn-sm btn-primary add-to-cart-btn" data-item='${JSON.stringify(item)}'>Add to Cart</button>
            <button class="btn btn-sm btn-outline" onclick="viewDetails('${item.id}')">Details</button>
          </div>
        </td>
      </tr>
    `).join('');
    
    // Add event listeners for new elements
    document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const item = JSON.parse(e.target.dataset.item);
        addToCart(item);
      });
    });
  }

  function addToCart(item) {
    if (!cartItems.find(cartItem => cartItem.id === item.id)) {
      cartItems.push(item);
      localStorage.setItem('cartItems', JSON.stringify(cartItems));
      updateCartCount();
      
      // Visual feedback
      showSuccess(`"${item.name}" added to cart`);
    }
  }

  function updateCartCount() {
    const cartCount = document.getElementById('cartCount');
    cartCount.textContent = cartItems.length;
  }

  function showLoading() {
    const resultsContainer = document.getElementById('resultsContainer');
    resultsContainer.innerHTML = '<div class="flex justify-center items-center py-12"><div class="spinner"></div></div>';
  }

  function hideLoading() {
    // Loading state is replaced by results or empty state
  }

  function showError(message) {
    const resultsContainer = document.getElementById('resultsContainer');
    resultsContainer.innerHTML = `
      <div class="text-center py-12">
        <div class="text-red-500 text-lg mb-2">⚠️</div>
        <p class="text-gray-600">${message}</p>
      </div>
    `;
  }

  function showSuccess(message) {
    // Create temporary success message
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
    successDiv.textContent = message;
    document.body.appendChild(successDiv);
    
    setTimeout(() => {
      successDiv.remove();
    }, 3000);
  }

  function formatAudienceSize(lower, upper) {
    if (!lower && !upper) return 'N/A';
    if (lower === upper) return formatNumber(lower);
    return `${formatNumber(lower)} - ${formatNumber(upper)}`;
  }

  function formatNumber(num) {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  }

  function formatPath(path) {
    if (!path || !Array.isArray(path)) return '';
    return path.join(' › ');
  }

  function formatTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now - time;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)} hr ago`;
    return `${Math.floor(diffMins / 1440)} day ago`;
  }

  // Initialize search history
  renderSearchHistory();
}

export default InterestSearchPage;