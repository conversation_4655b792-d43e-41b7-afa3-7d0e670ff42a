"""
Notification utilities for the TargetWise application.
Provides consistent logging and notification across the application.
"""
import asyncio
import json
import logging
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional, Union

# Add project root directory to path for Node.js notifications
project_root = Path(__file__).parent.parent.parent
scripts_dir = project_root / "scripts"

# Two approaches to import the JavaScript notify module
js_notify = None

# Method 1: Try to spawn a Node process
def node_notify(level, message, options=None):
    """Call the Node.js notification script via subprocess"""
    try:
        options_str = json.dumps(options or {})
        cmd = [
            "node", "-e",
            f"require('./scripts/notify').{level}('{message.replace("'", "\\'")}', {options_str})"
        ]
        subprocess.run(cmd, cwd=str(project_root), check=False, capture_output=True)
        return True
    except Exception:
        return False

# Method 2: Try to import via python-bridge if available
try:
    notify_script_path = scripts_dir / "notify.js"
    if notify_script_path.exists():
        # Only attempt this approach if the file exists
        try:
            # Method 2a: Using native module approach
            sys.path.append(str(scripts_dir))
            from notify import notify as js_notify_native
            js_notify = js_notify_native
        except ImportError:
            # Method 2b: Fall back to node subprocess
            js_notify = node_notify
    else:
        js_notify = None
except Exception:
    js_notify = None

class NotificationLevel:
    """Notification levels matching the JavaScript implementation."""
    DEBUG = "debug"
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    BUILD = "build"
    TEST = "test"
    AUDIT = "audit"
    GIT = "git"
    RESOURCE = "resource"

async def broadcast_to_web(level: str, message: str, context: Optional[str] = None, data: Optional[Dict[str, Any]] = None) -> None:
    """Broadcast a notification to all connected web clients."""
    try:
        # Import locally to avoid circular imports
        from api.api.v1.notification_routes import broadcast_notification

        # Determine notification title
        title = None
        if data and "title" in data:
            title = data["title"]

        # Determine duration
        duration = 5000
        if data and "duration" in data:
            duration = data["duration"]

        # Run broadcast in a background task
        asyncio.create_task(broadcast_notification(
            type_name=level,
            message=message,
            context=context,
            title=title,
            duration=duration
        ))
    except Exception as e:
        logger = logging.getLogger("targetwise.notify")
        logger.warning(
            f"Failed to broadcast notification to web clients: {e}", exc_info=True
        )

def notify(
    message: str,
    level: str = NotificationLevel.INFO,
    context: Optional[str] = None,
    data: Optional[Dict[str, Any]] = None,
    use_js: bool = True,
    use_logging: bool = True,
    use_web: bool = True
) -> None:
    """
    Send a notification with the specified level and context.

    Args:
        message: The message to log/notify
        level: The severity level (debug, info, success, warning, error, etc.)
        context: Additional context for the message
        data: Additional data to include in the notification
        use_js: Whether to use the JavaScript notifier (if available)
        use_logging: Whether to use Python's logging system
        use_web: Whether to broadcast to connected web clients
    """
    # Format the message with context if provided
    full_message = f"{f'[{context}] ' if context else ''}{message}"

    # Log to Python's logging system
    if use_logging:
        logger = logging.getLogger("targetwise")

        log_level = {
            NotificationLevel.DEBUG: logger.debug,
            NotificationLevel.INFO: logger.info,
            NotificationLevel.SUCCESS: logger.info,  # No success level in standard logging
            NotificationLevel.WARNING: logger.warning,
            NotificationLevel.ERROR: logger.error,
        }.get(level, logger.info)

        log_level(full_message, extra={"data": data} if data else {})

    # Use JavaScript notifier if available
    if use_js and js_notify:
        try:
            # Map Python logging levels to JS notification levels
            js_level = {
                "debug": "debug",
                "info": "info",
                "success": "success",
                "warning": "warning",
                "error": "error",
            }.get(level.lower(), "info")

            js_notify(js_level, full_message, {"context": context} if context else {})
        except Exception as e:
            logger = logging.getLogger("targetwise.notify")
            logger.warning(
                f"Failed to send JS notification: {e}", exc_info=True
            )

    # Broadcast to web clients if requested
    if use_web:
        try:
            # Check if we're in an async context
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Create the task only if we're in an async context
                asyncio.create_task(broadcast_to_web(level, message, context, data))
        except RuntimeError:
            # We're not in an async context, can't create a task
            pass

# Convenience functions
def notify_info(message: str, **kwargs) -> None:
    """Send an info notification."""
    notify(message, level=NotificationLevel.INFO, **kwargs)

def notify_success(message: str, **kwargs) -> None:
    """Send a success notification."""
    notify(message, level=NotificationLevel.SUCCESS, **kwargs)

def notify_warning(message: str, **kwargs) -> None:
    """Send a warning notification."""
    notify(message, level=NotificationLevel.WARNING, **kwargs)

def notify_error(message: str, **kwargs) -> None:
    """Send an error notification."""
    notify(message, level=NotificationLevel.ERROR, **kwargs)

def notify_debug(message: str, **kwargs) -> None:
    """Send a debug notification."""
    notify(message, level=NotificationLevel.DEBUG, **kwargs)
