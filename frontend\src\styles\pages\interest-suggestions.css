/* Interest Suggestions Page Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #f0f2f5;
    color: #1a1a2e;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: 700;
    color: #2563eb;
    text-decoration: none;
    transition: transform 0.2s;
}

.logo:hover {
    transform: translateY(-1px);
}

.logo svg {
    width: 32px;
    height: 32px;
}

.nav-menu {
    display: flex;
    gap: 8px;
    align-items: center;
}

.nav-link {
    padding: 8px 16px;
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s;
}

.nav-link:hover {
    color: #2563eb;
    background: rgba(37, 99, 235, 0.08);
}

.nav-link.active {
    color: #2563eb;
    background: rgba(37, 99, 235, 0.1);
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.theme-toggle {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: none;
    background: #f1f5f9;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.theme-toggle:hover {
    background: #e2e8f0;
}

.btn {
    padding: 10px 20px;
    border-radius: 8px;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-outline {
    background: transparent;
    color: #64748b;
    border: 2px solid #e2e8f0;
}

.btn-outline:hover {
    border-color: #cbd5e1;
    background: #f8fafc;
}

.btn-primary {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    color: white;
    box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.35);
}

/* Main Layout */
.main-container {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    gap: 24px;
    padding: 24px;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.sidebar-title {
    font-size: 14px;
    font-weight: 600;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 16px;
}

.sidebar-menu {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.sidebar-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 10px;
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    transition: all 0.2s;
}

.sidebar-item:hover {
    background: #f1f5f9;
    color: #1e293b;
    transform: translateX(4px);
}

.sidebar-item.active {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    color: #2563eb;
}

.sidebar-icon {
    width: 20px;
    height: 20px;
    opacity: 0.7;
}

/* Content Area */
.content {
    flex: 1;
}

/* Page Header */
.page-header {
    background: white;
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.page-header-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 16px;
}

.page-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.page-subtitle {
    color: #64748b;
    font-size: 16px;
    margin-left: 64px;
}

.page-actions {
    display: flex;
    gap: 12px;
}

.btn-secondary {
    background: #f1f5f9;
    color: #475569;
}

.btn-secondary:hover {
    background: #e2e8f0;
}

.staging-cart {
    background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
    color: white;
    position: relative;
}

.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc2626;
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 700;
}

/* Main Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

/* Seed Library Panel */
.seed-library {
    background: white;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.panel-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
}

.panel-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.panel-title {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
}

.search-box {
    margin-bottom: 24px;
}

.search-input-wrapper {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 14px 20px 14px 48px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.2s;
}

.search-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-input-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #94a3b8;
}

.seed-list {
    max-height: 400px;
    overflow-y: auto;
    margin-top: 20px;
}

.seed-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-radius: 10px;
    background: #f8fafc;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.seed-item:hover {
    background: #f1f5f9;
    transform: translateX(4px);
}

.seed-item.selected {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    border: 2px solid #2563eb;
}

.seed-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.seed-icon {
    width: 36px;
    height: 36px;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.seed-name {
    font-weight: 500;
    color: #1e293b;
}

.seed-category {
    font-size: 13px;
    color: #64748b;
}

.seed-count {
    background: #2563eb;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
}

.no-seeds {
    text-align: center;
    padding: 40px;
    color: #94a3b8;
}

/* Suggestions Panel */
.suggestions-panel {
    background: white;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.tabs {
    display: flex;
    gap: 8px;
    background: #f1f5f9;
    padding: 4px;
    border-radius: 10px;
    margin-bottom: 24px;
}

.tab {
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    background: transparent;
    color: #64748b;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    flex: 1;
}

.tab.active {
    background: white;
    color: #2563eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-group {
    margin-bottom: 24px;
}

.form-label {
    font-size: 14px;
    font-weight: 500;
    color: #475569;
    margin-bottom: 8px;
    display: block;
}

.select-wrapper {
    position: relative;
}

.form-select {
    width: 100%;
    padding: 14px 20px;
    padding-right: 48px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 16px;
    background: white;
    cursor: pointer;
    appearance: none;
    transition: all 0.2s;
}

.form-select:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.select-arrow {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: #94a3b8;
}

.btn-get-suggestions {
    width: 100%;
    padding: 16px;
    font-size: 16px;
    margin-bottom: 24px;
}

/* Suggestions Results */
.suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.suggestions-title {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
}

.suggestions-count {
    background: #f1f5f9;
    color: #475569;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
}

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
}

.suggestion-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.2s;
}

.suggestion-item:hover {
    border-color: #2563eb;
    background: rgba(37, 99, 235, 0.02);
}

.suggestion-item.selected {
    border-color: #2563eb;
    background: rgba(37, 99, 235, 0.05);
}

.suggestion-content {
    flex: 1;
}

.suggestion-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
}

.suggestion-meta {
    display: flex;
    gap: 16px;
    align-items: center;
}

.suggestion-stat {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 13px;
    color: #64748b;
}

.suggestion-stat-icon {
    width: 16px;
    height: 16px;
    opacity: 0.6;
}

.suggestion-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    background: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.action-btn:hover {
    border-color: #2563eb;
    color: #2563eb;
    background: rgba(37, 99, 235, 0.05);
}

.action-btn.add-btn {
    background: #2563eb;
    color: white;
    border-color: #2563eb;
}

.action-btn.add-btn:hover {
    background: #1d4ed8;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: #f1f5f9;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 36px;
}

.empty-title {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
}

.empty-text {
    color: #64748b;
}

/* Loading State */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f4f6;
    border-top-color: #2563eb;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Bulk Actions Bar */
.bulk-actions {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 2px solid #3b82f6;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.bulk-info {
    font-weight: 500;
    color: #1e40af;
}

.bulk-buttons {
    display: flex;
    gap: 8px;
}

/* Responsive */
@media (max-width: 1200px) {
    .content-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 1024px) {
    .sidebar {
        display: none;
    }
    
    .main-container {
        padding: 16px;
    }
}

@media (max-width: 640px) {
    .header-content {
        padding: 12px 16px;
    }
    
    .nav-menu {
        display: none;
    }
    
    .page-header {
        padding: 24px 16px;
    }
    
    .page-title {
        font-size: 24px;
    }
    
    .seed-library,
    .suggestions-panel {
        padding: 24px 16px;
    }
}