/* TargetWise Design System - Base Styles
   ======================================
   Core styles and CSS reset
*/

/* CSS Reset & Base Styles */
*, *::before, *::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-sans);
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
  color: var(--color-gray-700);
  background-color: var(--color-white);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 var(--spacing-base) 0;
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-gray-800);
}

h1 { font-size: var(--font-size-h1); }
h2 { font-size: var(--font-size-h2); }
h3 { font-size: var(--font-size-h3); font-weight: var(--font-weight-semibold); }
h4 { font-size: var(--font-size-h4); font-weight: var(--font-weight-semibold); }

p {
  margin: 0 0 var(--spacing-base) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-hover);
  text-decoration: underline;
}

/* Lists */
ul, ol {
  margin: 0 0 var(--spacing-base) 0;
  padding-left: var(--spacing-xl);
}

li {
  margin-bottom: var(--spacing-xs);
}

/* Code & Pre */
code {
  font-family: var(--font-mono);
  font-size: 0.875em;
  padding: 2px 6px;
  background-color: var(--color-gray-100);
  border-radius: var(--radius-sm);
}

pre {
  font-family: var(--font-mono);
  font-size: var(--font-size-small);
  line-height: var(--line-height-relaxed);
  padding: var(--spacing-base);
  background-color: var(--color-gray-50);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  overflow-x: auto;
}

pre code {
  padding: 0;
  background: none;
  border-radius: 0;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Tables */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-base);
}

th, td {
  text-align: left;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-gray-100);
}

th {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  background-color: var(--color-gray-50);
}

/* Forms */
label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="date"],
input[type="time"],
input[type="datetime-local"],
textarea,
select {
  width: 100%;
  padding: var(--input-padding-y) var(--input-padding-x);
  font-family: inherit;
  font-size: var(--font-size-body);
  line-height: var(--line-height-normal);
  color: var(--color-gray-700);
  background-color: var(--color-white);
  border: var(--input-border);
  border-radius: var(--radius-xl);
  transition: all var(--transition-fast);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="date"]:focus,
input[type="time"]:focus,
input[type="datetime-local"]:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

textarea {
  min-height: 120px;
  resize: vertical;
}

select {
  cursor: pointer;
}

/* Placeholders */
::placeholder {
  color: var(--color-gray-400);
  opacity: 1;
}

/* Selection */
::selection {
  background-color: var(--color-primary-light);
  color: var(--color-gray-900);
}

/* Layout Containers */
.tw-container {
  width: 100%;
  max-width: var(--content-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-base);
}

@media (min-width: 1024px) {
  .tw-container {
    padding: 0 var(--spacing-xl);
  }
}

/* Grid System */
.tw-grid {
  display: grid;
  gap: var(--spacing-xl);
}

.tw-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.tw-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.tw-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.tw-grid-auto {
  grid-template-columns: repeat(auto-fit, minmax(var(--min-card-width), 1fr));
}

/* Responsive Grid */
@media (max-width: 1023px) {
  .tw-grid-2,
  .tw-grid-3,
  .tw-grid-4 {
    grid-template-columns: 1fr;
  }
}

/* Utility: Screen Reader Only */
.tw-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Focus Visible */
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-50);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* Print Styles */
@media print {
  body {
    color: black;
    background: white;
  }
  
  .no-print {
    display: none !important;
  }
}